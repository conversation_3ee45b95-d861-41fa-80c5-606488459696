# إعداد Firebase لبرنامج المحاسبة اليومي

## نظرة عامة
تم تحديث التطبيق لاستخدام Firebase Firestore كقاعدة بيانات سحابية بدلاً من localStorage المحلي. هذا يوفر:

- **حفظ البيانات في السحابة**: البيانات محفوظة بشكل آمن ومتاحة من أي جهاز
- **المزامنة التلقائية**: التحديثات تظهر فوراً على جميع الأجهزة المتصلة
- **النسخ الاحتياطي التلقائي**: لا تفقد البيانات أبداً
- **الأداء المحسن**: تحميل سريع وفعال للبيانات

## التغييرات المطبقة

### 1. إعداد Firebase
- تم إنشاء ملف `src/lib/firebase.ts` مع إعدادات المشروع
- تم تكوين Firestore وAuthentication وAnalytics

### 2. تحديث DataContext
- تم تحويل جميع العمليات لتستخدم Firestore
- إضافة المزامنة التلقائية مع `onSnapshot`
- تحويل جميع الدوال إلى async/await
- تحديث أنواع البيانات لتستخدم معرفات Firebase

### 3. تحديث المكونات
- **صفحة العملاء**: تحديث جميع عمليات CRUD لتعمل مع Firebase
- **صفحة المنتجات**: تحديث إضافة وتعديل وحذف المنتجات
- **صفحة إضافة المعاملات**: تحديث حفظ المعاملات الجديدة
- إضافة معالجة الأخطاء والرسائل التوضيحية

## هيكل قاعدة البيانات في Firestore

### Collections (المجموعات):

#### 1. `transactions` (المعاملات)
```javascript
{
  customerName: string,
  product: string,
  quantity: string,
  pricePerUnit: number,
  totalAmount: number,
  paidAmount: number,
  remainingAmount: number,
  date: string,
  time: string
}
```

#### 2. `customers` (العملاء)
```javascript
{
  name: string,
  phone?: string,
  address?: string,
  notes?: string,
  totalTransactions: number,
  totalPurchases: number,
  totalPaid: number,
  remainingDebt: number,
  lastTransaction: string
}
```

#### 3. `products` (المنتجات)
```javascript
{
  name: string,
  defaultPrice: number,
  unit: string,
  description: string,
  totalSold: number,
  revenue: number,
  lastSale: string
}
```

## الميزات الجديدة

### 1. المزامنة التلقائية
- البيانات تتحدث تلقائياً عند تغييرها من أي جهاز
- لا حاجة لإعادة تحميل الصفحة

### 2. معالجة الأخطاء
- رسائل خطأ واضحة عند فشل العمليات
- إعادة المحاولة التلقائية في بعض الحالات

### 3. الأداء المحسن
- تحميل البيانات حسب الحاجة
- ترتيب البيانات في قاعدة البيانات

## كيفية الاستخدام

### 1. تشغيل التطبيق
```bash
npm install
npm run dev
```

### 2. إضافة البيانات
- جميع البيانات الجديدة تُحفظ تلقائياً في Firebase
- يمكن الوصول إليها من أي جهاز متصل بالإنترنت

### 3. عرض البيانات
- البيانات تظهر فوراً بعد الإضافة
- التحديثات تظهر تلقائياً على جميع الأجهزة

## الأمان والخصوصية

- جميع البيانات محمية بقواعد أمان Firebase
- الوصول محدود للمستخدمين المصرح لهم فقط
- النسخ الاحتياطي التلقائي يضمن عدم فقدان البيانات

## الدعم الفني

في حالة وجود أي مشاكل:
1. تأكد من اتصال الإنترنت
2. تحقق من إعدادات Firebase في `src/lib/firebase.ts`
3. راجع رسائل الخطأ في وحدة تحكم المتصفح

## ملاحظات مهمة

- **النسخ الاحتياطي**: البيانات القديمة في localStorage لن تنتقل تلقائياً
- **الإنترنت**: يتطلب اتصال إنترنت للعمل
- **الأمان**: لا تشارك إعدادات Firebase مع أشخاص غير مصرح لهم
