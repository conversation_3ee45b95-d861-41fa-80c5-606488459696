import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  onSnapshot,
  query,
  orderBy
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

// تعريف أنواع البيانات
export interface Transaction {
  id?: string; // Firebase document ID
  customerName: string;
  product: string;
  quantity: string;
  pricePerUnit: number;
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
  date: string;
  time: string;
}

export interface Customer {
  id?: string; // Firebase document ID
  name: string;
  phone?: string;
  address?: string;
  notes?: string;
  totalTransactions: number;
  totalPurchases: number;
  totalPaid: number;
  remainingDebt: number;
  lastTransaction: string;
}

export interface ProductMaterial {
  materialId: string;
  materialName: string;
  quantityNeeded: number; // الكمية المطلوبة من الخامة لصنع وحدة واحدة من المنتج
}

export interface Product {
  id?: string; // Firebase document ID
  name: string;
  defaultPrice: number;
  unit: string;
  description: string;
  totalSold: number;
  revenue: number;
  lastSale: string;
  materials: ProductMaterial[]; // الخامات المطلوبة لصنع المنتج
}

export interface Material {
  id?: string; // Firebase document ID
  name: string;
  category: string;
  unit: string; // وحدة القياس (كيلو، جرام، قطعة، عبوة)
  currentStock: number; // المخزون الحالي
  availableQuantity: number; // عدد المتوفر من الخامة
  unitCost: number; // تكلفة الوحدة
  supplier: string; // المورد
  location: string; // موقع التخزين
  description?: string;
  lastUpdated: string;
  totalPurchased: number; // إجمالي المشتريات
  totalUsed: number; // إجمالي المستخدم
  totalCost: number; // إجمالي التكلفة
}

export interface StockTransaction {
  id?: string; // Firebase document ID
  materialId: string;
  materialName: string;
  type: 'in' | 'out'; // دخول أو خروج
  quantity: number;
  unitCost?: number; // للمشتريات
  totalCost?: number; // إجمالي التكلفة
  reason: string; // سبب الحركة
  reference?: string; // مرجع (رقم فاتورة، أمر عمل، إلخ)
  date: string;
  time: string;
  notes?: string;
  balanceAfter: number; // الرصيد بعد العملية
}

// تعريف نوع السياق
interface DataContextType {
  transactions: Transaction[];
  customers: Customer[];
  products: Product[];
  materials: Material[];
  stockTransactions: StockTransaction[];
  addTransaction: (transaction: Omit<Transaction, 'id'>) => Promise<void>;
  updateTransaction: (id: string, transaction: Omit<Transaction, 'id'>) => Promise<void>;
  deleteTransaction: (id: string) => Promise<void>;
  addProduct: (product: Omit<Product, 'id' | 'totalSold' | 'revenue' | 'lastSale'>) => Promise<void>;
  updateProduct: (id: string, product: Omit<Product, 'id' | 'totalSold' | 'revenue' | 'lastSale'>) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;
  addCustomer: (customer: Omit<Customer, 'id' | 'totalTransactions' | 'totalPurchases' | 'totalPaid' | 'remainingDebt' | 'lastTransaction'>) => Promise<void>;
  updateCustomer: (customer: Customer) => Promise<void>;
  deleteCustomer: (id: string) => Promise<void>;
  addMaterial: (material: Omit<Material, 'id' | 'currentStock' | 'totalPurchased' | 'totalUsed' | 'totalCost' | 'lastUpdated'>) => Promise<void>;
  updateMaterial: (id: string, material: Partial<Material>) => Promise<void>;
  deleteMaterial: (id: string) => Promise<void>;
  addStockTransaction: (stockTransaction: Omit<StockTransaction, 'id' | 'balanceAfter'>) => Promise<void>;
  updateStockTransaction: (id: string, stockTransaction: Partial<StockTransaction>) => Promise<void>;
  deleteStockTransaction: (id: string) => Promise<void>;
  getStats: () => {
    todaySales: number;
    totalDebts: number;
    thisMonthSales: number;
    customersCount: number;
  };
  getMaterialStats: () => {
    totalMaterials: number;
    lowStockMaterials: number;
    totalStockValue: number;
    totalAvailableQuantity: number;
    totalStockTransactions: number;
  };
}

// إنشاء السياق
const DataContext = createContext<DataContextType | undefined>(undefined);

// مزود السياق
export const DataProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [materials, setMaterials] = useState<Material[]>([]);
  const [stockTransactions, setStockTransactions] = useState<StockTransaction[]>([]);

  // تحميل البيانات من Firestore عند بدء التطبيق
  useEffect(() => {
    // تحميل المعاملات
    const transactionsQuery = query(collection(db, 'transactions'), orderBy('date', 'desc'));
    const unsubscribeTransactions = onSnapshot(transactionsQuery, (snapshot) => {
      const transactionsData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Transaction[];
      setTransactions(transactionsData);
    });

    // تحميل العملاء
    const customersQuery = query(collection(db, 'customers'), orderBy('name'));
    const unsubscribeCustomers = onSnapshot(customersQuery, (snapshot) => {
      const customersData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Customer[];
      setCustomers(customersData);
    });

    // تحميل المنتجات
    const productsQuery = query(collection(db, 'products'), orderBy('name'));
    const unsubscribeProducts = onSnapshot(productsQuery, (snapshot) => {
      const productsData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Product[];
      setProducts(productsData);
    });

    // تحميل الخامات
    const materialsQuery = query(collection(db, 'materials'), orderBy('name'));
    const unsubscribeMaterials = onSnapshot(materialsQuery, (snapshot) => {
      const materialsData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Material[];
      setMaterials(materialsData);
    });

    // تحميل حركات المخزون
    const stockTransactionsQuery = query(collection(db, 'stockTransactions'), orderBy('date', 'desc'));
    const unsubscribeStockTransactions = onSnapshot(stockTransactionsQuery, (snapshot) => {
      const stockTransactionsData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as StockTransaction[];
      setStockTransactions(stockTransactionsData);
    });

    // تنظيف المستمعين عند إلغاء تحميل المكون
    return () => {
      unsubscribeTransactions();
      unsubscribeCustomers();
      unsubscribeProducts();
      unsubscribeMaterials();
      unsubscribeStockTransactions();
    };
  }, []);

  // إضافة معاملة جديدة
  const addTransaction = async (transactionData: Omit<Transaction, 'id'>) => {
    try {
      // إضافة المعاملة إلى Firestore
      await addDoc(collection(db, 'transactions'), transactionData);

      // تحديث أو إضافة العميل
      const existingCustomer = customers.find(c => c.name === transactionData.customerName);

      if (existingCustomer && existingCustomer.id) {
        // تحديث العميل الموجود
        const updatedCustomerData = {
          name: existingCustomer.name,
          phone: existingCustomer.phone,
          address: existingCustomer.address,
          notes: existingCustomer.notes,
          totalTransactions: existingCustomer.totalTransactions + 1,
          totalPurchases: existingCustomer.totalPurchases + transactionData.totalAmount,
          totalPaid: existingCustomer.totalPaid + transactionData.paidAmount,
          remainingDebt: existingCustomer.remainingDebt + transactionData.remainingAmount,
          lastTransaction: transactionData.date,
        };
        await updateDoc(doc(db, 'customers', existingCustomer.id), updatedCustomerData);
      } else {
        // إضافة عميل جديد
        const newCustomer: Omit<Customer, 'id'> = {
          name: transactionData.customerName,
          totalTransactions: 1,
          totalPurchases: transactionData.totalAmount,
          totalPaid: transactionData.paidAmount,
          remainingDebt: transactionData.remainingAmount,
          lastTransaction: transactionData.date,
        };
        await addDoc(collection(db, 'customers'), newCustomer);
      }

      // تحديث المنتج إذا كان موجوداً
      const existingProduct = products.find(p => p.name === transactionData.product);
      if (existingProduct && existingProduct.id) {
        const updatedProductData = {
          name: existingProduct.name,
          defaultPrice: existingProduct.defaultPrice,
          unit: existingProduct.unit,
          description: existingProduct.description,
          totalSold: existingProduct.totalSold + parseFloat(transactionData.quantity),
          revenue: existingProduct.revenue + transactionData.totalAmount,
          lastSale: transactionData.date,
          materials: existingProduct.materials || [],
        };
        await updateDoc(doc(db, 'products', existingProduct.id), updatedProductData);

        // خصم الخامات من المخزون
        if (existingProduct.materials && existingProduct.materials.length > 0) {
          const soldQuantity = parseFloat(transactionData.quantity);

          for (const productMaterial of existingProduct.materials) {
            const material = materials.find(m => m.id === productMaterial.materialId);
            if (material && material.id) {
              const requiredQuantity = productMaterial.quantityNeeded * soldQuantity;

              // التحقق من توفر الكمية المطلوبة
              if (material.currentStock >= requiredQuantity) {
                const newCurrentStock = material.currentStock - requiredQuantity;
                const newAvailableQuantity = Math.max(0, material.availableQuantity - requiredQuantity);

                // تحديث المخزون
                const updatedMaterialData = {
                  ...material,
                  currentStock: newCurrentStock,
                  availableQuantity: newAvailableQuantity,
                  totalUsed: material.totalUsed + requiredQuantity,
                  lastUpdated: new Date().toISOString().split('T')[0],
                };
                await updateDoc(doc(db, 'materials', material.id), updatedMaterialData);

                // إضافة حركة مخزون للخصم
                const stockTransactionData = {
                  materialId: material.id,
                  materialName: material.name,
                  type: 'out' as const,
                  quantity: requiredQuantity,
                  reason: `بيع منتج: ${existingProduct.name}`,
                  reference: `معاملة رقم: ${transactionData.date}-${transactionData.customerName}`,
                  date: new Date().toISOString().split('T')[0],
                  time: new Date().toLocaleTimeString('ar-EG'),
                  notes: `خصم تلقائي عند بيع ${soldQuantity} ${existingProduct.unit} من ${existingProduct.name}`,
                  balanceAfter: newCurrentStock,
                };
                await addDoc(collection(db, 'stockTransactions'), stockTransactionData);
              } else {
                console.warn(`تحذير: المخزون غير كافي للخامة ${material.name}. المطلوب: ${requiredQuantity}, المتوفر: ${material.currentStock}`);
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('Error adding transaction:', error);
      throw error;
    }
  };

  // تحديث معاملة موجودة
  const updateTransaction = async (id: string, transactionData: Omit<Transaction, 'id'>) => {
    try {
      await updateDoc(doc(db, 'transactions', id), transactionData);
    } catch (error) {
      console.error('Error updating transaction:', error);
      throw error;
    }
  };

  // حذف معاملة
  const deleteTransaction = async (id: string) => {
    try {
      await deleteDoc(doc(db, 'transactions', id));
    } catch (error) {
      console.error('Error deleting transaction:', error);
      throw error;
    }
  };

  // إضافة منتج جديد
  const addProduct = async (productData: Omit<Product, 'id' | 'totalSold' | 'revenue' | 'lastSale'>) => {
    try {
      const newProductData = {
        ...productData,
        totalSold: 0,
        revenue: 0,
        lastSale: '',
        materials: productData.materials || [],
      };
      await addDoc(collection(db, 'products'), newProductData);
    } catch (error) {
      console.error('Error adding product:', error);
      throw error;
    }
  };

  // تحديث منتج موجود
  const updateProduct = async (id: string, productData: Omit<Product, 'id' | 'totalSold' | 'revenue' | 'lastSale'>) => {
    try {
      const existingProduct = products.find(p => p.id === id);
      if (existingProduct) {
        const updatedData = {
          ...productData,
          totalSold: existingProduct.totalSold,
          revenue: existingProduct.revenue,
          lastSale: existingProduct.lastSale,
        };
        await updateDoc(doc(db, 'products', id), updatedData);
      }
    } catch (error) {
      console.error('Error updating product:', error);
      throw error;
    }
  };

  // حذف منتج
  const deleteProduct = async (id: string) => {
    try {
      await deleteDoc(doc(db, 'products', id));
    } catch (error) {
      console.error('Error deleting product:', error);
      throw error;
    }
  };

  // إضافة عميل جديد
  const addCustomer = async (customerData: Omit<Customer, 'id' | 'totalTransactions' | 'totalPurchases' | 'totalPaid' | 'remainingDebt' | 'lastTransaction'>) => {
    try {
      const newCustomerData = {
        ...customerData,
        totalTransactions: 0,
        totalPurchases: 0,
        totalPaid: 0,
        remainingDebt: 0,
        lastTransaction: '',
      };
      await addDoc(collection(db, 'customers'), newCustomerData);
    } catch (error) {
      console.error('Error adding customer:', error);
      throw error;
    }
  };

  // تحديث عميل
  const updateCustomer = async (updatedCustomer: Customer) => {
    try {
      if (updatedCustomer.id) {
        const updateData = {
          name: updatedCustomer.name,
          phone: updatedCustomer.phone,
          address: updatedCustomer.address,
          notes: updatedCustomer.notes,
          totalTransactions: updatedCustomer.totalTransactions,
          totalPurchases: updatedCustomer.totalPurchases,
          totalPaid: updatedCustomer.totalPaid,
          remainingDebt: updatedCustomer.remainingDebt,
          lastTransaction: updatedCustomer.lastTransaction,
        };
        await updateDoc(doc(db, 'customers', updatedCustomer.id), updateData);
      }
    } catch (error) {
      console.error('Error updating customer:', error);
      throw error;
    }
  };

  // حذف عميل
  const deleteCustomer = async (id: string) => {
    try {
      await deleteDoc(doc(db, 'customers', id));
    } catch (error) {
      console.error('Error deleting customer:', error);
      throw error;
    }
  };

  // إضافة خامة جديدة
  const addMaterial = async (materialData: Omit<Material, 'id' | 'currentStock' | 'totalPurchased' | 'totalUsed' | 'totalCost' | 'lastUpdated'>) => {
    try {
      const now = new Date();
      const newMaterialData = {
        ...materialData,
        currentStock: materialData.availableQuantity || 0,
        totalPurchased: 0,
        totalUsed: 0,
        totalCost: 0,
        lastUpdated: now.toISOString().split('T')[0],
      };
      await addDoc(collection(db, 'materials'), newMaterialData);
    } catch (error) {
      console.error('Error adding material:', error);
      throw error;
    }
  };

  // تحديث خامة
  const updateMaterial = async (id: string, materialData: Partial<Material>) => {
    try {
      const updateData = {
        ...materialData,
        lastUpdated: new Date().toISOString().split('T')[0],
      };
      await updateDoc(doc(db, 'materials', id), updateData);
    } catch (error) {
      console.error('Error updating material:', error);
      throw error;
    }
  };

  // حذف خامة
  const deleteMaterial = async (id: string) => {
    try {
      await deleteDoc(doc(db, 'materials', id));
    } catch (error) {
      console.error('Error deleting material:', error);
      throw error;
    }
  };

  // إضافة حركة مخزون
  const addStockTransaction = async (stockTransactionData: Omit<StockTransaction, 'id' | 'balanceAfter'>) => {
    try {
      // العثور على الخامة
      const material = materials.find(m => m.id === stockTransactionData.materialId);
      if (!material) {
        throw new Error('Material not found');
      }

      // حساب الرصيد الجديد
      const newBalance = stockTransactionData.type === 'in'
        ? material.currentStock + stockTransactionData.quantity
        : material.currentStock - stockTransactionData.quantity;

      if (newBalance < 0) {
        throw new Error('Insufficient stock');
      }

      // إضافة حركة المخزون
      const newStockTransactionData = {
        ...stockTransactionData,
        balanceAfter: newBalance,
      };
      await addDoc(collection(db, 'stockTransactions'), newStockTransactionData);

      // تحديث رصيد الخامة
      const materialUpdateData: Partial<Material> = {
        currentStock: newBalance,
        lastUpdated: new Date().toISOString().split('T')[0],
      };

      if (stockTransactionData.type === 'in') {
        materialUpdateData.totalPurchased = (material.totalPurchased || 0) + stockTransactionData.quantity;
        if (stockTransactionData.unitCost) {
          materialUpdateData.totalCost = (material.totalCost || 0) + (stockTransactionData.quantity * stockTransactionData.unitCost);
          materialUpdateData.unitCost = stockTransactionData.unitCost;
        }
      } else {
        materialUpdateData.totalUsed = (material.totalUsed || 0) + stockTransactionData.quantity;
      }

      await updateDoc(doc(db, 'materials', stockTransactionData.materialId), materialUpdateData);
    } catch (error) {
      console.error('Error adding stock transaction:', error);
      throw error;
    }
  };

  // تحديث حركة مخزون
  const updateStockTransaction = async (id: string, stockTransactionData: Partial<StockTransaction>) => {
    try {
      await updateDoc(doc(db, 'stockTransactions', id), stockTransactionData);
    } catch (error) {
      console.error('Error updating stock transaction:', error);
      throw error;
    }
  };

  // حذف حركة مخزون
  const deleteStockTransaction = async (id: string) => {
    try {
      await deleteDoc(doc(db, 'stockTransactions', id));
    } catch (error) {
      console.error('Error deleting stock transaction:', error);
      throw error;
    }
  };

  // حساب الإحصائيات
  const getStats = () => {
    const today = new Date().toISOString().split('T')[0];
    const thisMonth = new Date().toISOString().slice(0, 7); // YYYY-MM

    const todaySales = transactions
      .filter(t => t.date === today)
      .reduce((sum, t) => sum + t.totalAmount, 0);

    const thisMonthSales = transactions
      .filter(t => t.date.startsWith(thisMonth))
      .reduce((sum, t) => sum + t.totalAmount, 0);

    const totalDebts = customers.reduce((sum, c) => sum + c.remainingDebt, 0);

    return {
      todaySales,
      totalDebts,
      thisMonthSales,
      customersCount: customers.length,
    };
  };

  // حساب إحصائيات الخامات
  const getMaterialStats = () => {
    const totalMaterials = materials.length;
    const lowStockMaterials = materials.filter(m => m.currentStock <= 10).length; // خامات أقل من 10 وحدات
    const totalStockValue = materials.reduce((sum, m) => sum + (m.currentStock * m.unitCost), 0);
    const totalAvailableQuantity = materials.reduce((sum, m) => sum + m.availableQuantity, 0);
    const totalStockTransactions = stockTransactions.length;

    return {
      totalMaterials,
      lowStockMaterials,
      totalStockValue,
      totalAvailableQuantity,
      totalStockTransactions,
    };
  };

  const value: DataContextType = {
    transactions,
    customers,
    products,
    materials,
    stockTransactions,
    addTransaction,
    updateTransaction,
    deleteTransaction,
    addProduct,
    updateProduct,
    deleteProduct,
    addCustomer,
    updateCustomer,
    deleteCustomer,
    addMaterial,
    updateMaterial,
    deleteMaterial,
    addStockTransaction,
    updateStockTransaction,
    deleteStockTransaction,
    getStats,
    getMaterialStats,
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
};

// Hook لاستخدام السياق
export const useData = () => {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};
