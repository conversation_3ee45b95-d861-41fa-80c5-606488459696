import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// تعريف أنواع البيانات
export interface Transaction {
  id: number;
  customerName: string;
  product: string;
  quantity: string;
  pricePerUnit: number;
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
  date: string;
  time: string;
}

export interface Customer {
  id: number;
  name: string;
  totalTransactions: number;
  totalPurchases: number;
  totalPaid: number;
  remainingDebt: number;
  lastTransaction: string;
}

export interface Product {
  id: number;
  name: string;
  defaultPrice: number;
  unit: string;
  description: string;
  totalSold: number;
  revenue: number;
  lastSale: string;
}

// تعريف نوع السياق
interface DataContextType {
  transactions: Transaction[];
  customers: Customer[];
  products: Product[];
  addTransaction: (transaction: Omit<Transaction, 'id'>) => void;
  addProduct: (product: Omit<Product, 'id' | 'totalSold' | 'revenue' | 'lastSale'>) => void;
  updateProduct: (id: number, product: Omit<Product, 'id' | 'totalSold' | 'revenue' | 'lastSale'>) => void;
  deleteProduct: (id: number) => void;
  updateCustomer: (customer: Customer) => void;
  deleteCustomer: (id: number) => void;
  getStats: () => {
    todaySales: number;
    totalDebts: number;
    thisMonthSales: number;
    customersCount: number;
  };
}

// إنشاء السياق
const DataContext = createContext<DataContextType | undefined>(undefined);

// مزود السياق
export const DataProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [products, setProducts] = useState<Product[]>([]);

  // تحميل البيانات من localStorage عند بدء التطبيق
  useEffect(() => {
    const savedTransactions = localStorage.getItem('transactions');
    const savedCustomers = localStorage.getItem('customers');
    const savedProducts = localStorage.getItem('products');

    if (savedTransactions) {
      setTransactions(JSON.parse(savedTransactions));
    }
    if (savedCustomers) {
      setCustomers(JSON.parse(savedCustomers));
    }
    if (savedProducts) {
      setProducts(JSON.parse(savedProducts));
    }
  }, []);

  // حفظ البيانات في localStorage عند تغييرها
  useEffect(() => {
    localStorage.setItem('transactions', JSON.stringify(transactions));
  }, [transactions]);

  useEffect(() => {
    localStorage.setItem('customers', JSON.stringify(customers));
  }, [customers]);

  useEffect(() => {
    localStorage.setItem('products', JSON.stringify(products));
  }, [products]);

  // إضافة معاملة جديدة
  const addTransaction = (transactionData: Omit<Transaction, 'id'>) => {
    const newTransaction: Transaction = {
      ...transactionData,
      id: Date.now(), // استخدام timestamp كـ ID مؤقت
    };

    setTransactions(prev => [...prev, newTransaction]);

    // تحديث أو إضافة العميل
    const existingCustomerIndex = customers.findIndex(c => c.name === transactionData.customerName);
    
    if (existingCustomerIndex >= 0) {
      // تحديث العميل الموجود
      const updatedCustomers = [...customers];
      const customer = updatedCustomers[existingCustomerIndex];
      customer.totalTransactions += 1;
      customer.totalPurchases += transactionData.totalAmount;
      customer.totalPaid += transactionData.paidAmount;
      customer.remainingDebt += transactionData.remainingAmount;
      customer.lastTransaction = transactionData.date;
      setCustomers(updatedCustomers);
    } else {
      // إضافة عميل جديد
      const newCustomer: Customer = {
        id: Date.now() + 1,
        name: transactionData.customerName,
        totalTransactions: 1,
        totalPurchases: transactionData.totalAmount,
        totalPaid: transactionData.paidAmount,
        remainingDebt: transactionData.remainingAmount,
        lastTransaction: transactionData.date,
      };
      setCustomers(prev => [...prev, newCustomer]);
    }

    // تحديث المنتج إذا كان موجوداً
    const existingProductIndex = products.findIndex(p => p.name === transactionData.product);
    if (existingProductIndex >= 0) {
      const updatedProducts = [...products];
      const product = updatedProducts[existingProductIndex];
      product.totalSold += parseFloat(transactionData.quantity);
      product.revenue += transactionData.totalAmount;
      product.lastSale = transactionData.date;
      setProducts(updatedProducts);
    }
  };

  // إضافة منتج جديد
  const addProduct = (productData: Omit<Product, 'id' | 'totalSold' | 'revenue' | 'lastSale'>) => {
    const newProduct: Product = {
      ...productData,
      id: Date.now(),
      totalSold: 0,
      revenue: 0,
      lastSale: '',
    };
    setProducts(prev => [...prev, newProduct]);
  };

  // تحديث منتج موجود
  const updateProduct = (id: number, productData: Omit<Product, 'id' | 'totalSold' | 'revenue' | 'lastSale'>) => {
    setProducts(prev => prev.map(product =>
      product.id === id
        ? { ...product, ...productData }
        : product
    ));
  };

  // حذف منتج
  const deleteProduct = (id: number) => {
    setProducts(prev => prev.filter(product => product.id !== id));
  };

  // تحديث عميل
  const updateCustomer = (updatedCustomer: Customer) => {
    setCustomers(prev =>
      prev.map(customer =>
        customer.id === updatedCustomer.id ? updatedCustomer : customer
      )
    );
  };

  // حذف عميل
  const deleteCustomer = (id: number) => {
    setCustomers(prev => prev.filter(customer => customer.id !== id));
  };

  // حساب الإحصائيات
  const getStats = () => {
    const today = new Date().toISOString().split('T')[0];
    const thisMonth = new Date().toISOString().slice(0, 7); // YYYY-MM

    const todaySales = transactions
      .filter(t => t.date === today)
      .reduce((sum, t) => sum + t.totalAmount, 0);

    const thisMonthSales = transactions
      .filter(t => t.date.startsWith(thisMonth))
      .reduce((sum, t) => sum + t.totalAmount, 0);

    const totalDebts = customers.reduce((sum, c) => sum + c.remainingDebt, 0);

    return {
      todaySales,
      totalDebts,
      thisMonthSales,
      customersCount: customers.length,
    };
  };

  const value: DataContextType = {
    transactions,
    customers,
    products,
    addTransaction,
    addProduct,
    updateProduct,
    deleteProduct,
    updateCustomer,
    deleteCustomer,
    getStats,
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
};

// Hook لاستخدام السياق
export const useData = () => {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};
