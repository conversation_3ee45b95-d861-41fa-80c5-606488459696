import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  onSnapshot,
  query,
  orderBy
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

// تعريف أنواع البيانات
export interface Transaction {
  id?: string; // Firebase document ID
  customerName: string;
  product: string;
  quantity: string;
  pricePerUnit: number;
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
  date: string;
  time: string;
}

export interface Customer {
  id?: string; // Firebase document ID
  name: string;
  phone?: string;
  address?: string;
  notes?: string;
  totalTransactions: number;
  totalPurchases: number;
  totalPaid: number;
  remainingDebt: number;
  lastTransaction: string;
}

export interface Product {
  id?: string; // Firebase document ID
  name: string;
  defaultPrice: number;
  unit: string;
  description: string;
  totalSold: number;
  revenue: number;
  lastSale: string;
}

// تعريف نوع السياق
interface DataContextType {
  transactions: Transaction[];
  customers: Customer[];
  products: Product[];
  addTransaction: (transaction: Omit<Transaction, 'id'>) => Promise<void>;
  addProduct: (product: Omit<Product, 'id' | 'totalSold' | 'revenue' | 'lastSale'>) => Promise<void>;
  updateProduct: (id: string, product: Omit<Product, 'id' | 'totalSold' | 'revenue' | 'lastSale'>) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;
  addCustomer: (customer: Omit<Customer, 'id' | 'totalTransactions' | 'totalPurchases' | 'totalPaid' | 'remainingDebt' | 'lastTransaction'>) => Promise<void>;
  updateCustomer: (customer: Customer) => Promise<void>;
  deleteCustomer: (id: string) => Promise<void>;
  getStats: () => {
    todaySales: number;
    totalDebts: number;
    thisMonthSales: number;
    customersCount: number;
  };
}

// إنشاء السياق
const DataContext = createContext<DataContextType | undefined>(undefined);

// مزود السياق
export const DataProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [products, setProducts] = useState<Product[]>([]);

  // تحميل البيانات من Firestore عند بدء التطبيق
  useEffect(() => {
    // تحميل المعاملات
    const transactionsQuery = query(collection(db, 'transactions'), orderBy('date', 'desc'));
    const unsubscribeTransactions = onSnapshot(transactionsQuery, (snapshot) => {
      const transactionsData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Transaction[];
      setTransactions(transactionsData);
    });

    // تحميل العملاء
    const customersQuery = query(collection(db, 'customers'), orderBy('name'));
    const unsubscribeCustomers = onSnapshot(customersQuery, (snapshot) => {
      const customersData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Customer[];
      setCustomers(customersData);
    });

    // تحميل المنتجات
    const productsQuery = query(collection(db, 'products'), orderBy('name'));
    const unsubscribeProducts = onSnapshot(productsQuery, (snapshot) => {
      const productsData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Product[];
      setProducts(productsData);
    });

    // تنظيف المستمعين عند إلغاء تحميل المكون
    return () => {
      unsubscribeTransactions();
      unsubscribeCustomers();
      unsubscribeProducts();
    };
  }, []);

  // إضافة معاملة جديدة
  const addTransaction = async (transactionData: Omit<Transaction, 'id'>) => {
    try {
      // إضافة المعاملة إلى Firestore
      await addDoc(collection(db, 'transactions'), transactionData);

      // تحديث أو إضافة العميل
      const existingCustomer = customers.find(c => c.name === transactionData.customerName);

      if (existingCustomer && existingCustomer.id) {
        // تحديث العميل الموجود
        const updatedCustomerData = {
          name: existingCustomer.name,
          phone: existingCustomer.phone,
          address: existingCustomer.address,
          notes: existingCustomer.notes,
          totalTransactions: existingCustomer.totalTransactions + 1,
          totalPurchases: existingCustomer.totalPurchases + transactionData.totalAmount,
          totalPaid: existingCustomer.totalPaid + transactionData.paidAmount,
          remainingDebt: existingCustomer.remainingDebt + transactionData.remainingAmount,
          lastTransaction: transactionData.date,
        };
        await updateDoc(doc(db, 'customers', existingCustomer.id), updatedCustomerData);
      } else {
        // إضافة عميل جديد
        const newCustomer: Omit<Customer, 'id'> = {
          name: transactionData.customerName,
          totalTransactions: 1,
          totalPurchases: transactionData.totalAmount,
          totalPaid: transactionData.paidAmount,
          remainingDebt: transactionData.remainingAmount,
          lastTransaction: transactionData.date,
        };
        await addDoc(collection(db, 'customers'), newCustomer);
      }

      // تحديث المنتج إذا كان موجوداً
      const existingProduct = products.find(p => p.name === transactionData.product);
      if (existingProduct && existingProduct.id) {
        const updatedProductData = {
          name: existingProduct.name,
          defaultPrice: existingProduct.defaultPrice,
          unit: existingProduct.unit,
          description: existingProduct.description,
          totalSold: existingProduct.totalSold + parseFloat(transactionData.quantity),
          revenue: existingProduct.revenue + transactionData.totalAmount,
          lastSale: transactionData.date,
        };
        await updateDoc(doc(db, 'products', existingProduct.id), updatedProductData);
      }
    } catch (error) {
      console.error('Error adding transaction:', error);
      throw error;
    }
  };

  // إضافة منتج جديد
  const addProduct = async (productData: Omit<Product, 'id' | 'totalSold' | 'revenue' | 'lastSale'>) => {
    try {
      const newProductData = {
        ...productData,
        totalSold: 0,
        revenue: 0,
        lastSale: '',
      };
      await addDoc(collection(db, 'products'), newProductData);
    } catch (error) {
      console.error('Error adding product:', error);
      throw error;
    }
  };

  // تحديث منتج موجود
  const updateProduct = async (id: string, productData: Omit<Product, 'id' | 'totalSold' | 'revenue' | 'lastSale'>) => {
    try {
      const existingProduct = products.find(p => p.id === id);
      if (existingProduct) {
        const updatedData = {
          ...productData,
          totalSold: existingProduct.totalSold,
          revenue: existingProduct.revenue,
          lastSale: existingProduct.lastSale,
        };
        await updateDoc(doc(db, 'products', id), updatedData);
      }
    } catch (error) {
      console.error('Error updating product:', error);
      throw error;
    }
  };

  // حذف منتج
  const deleteProduct = async (id: string) => {
    try {
      await deleteDoc(doc(db, 'products', id));
    } catch (error) {
      console.error('Error deleting product:', error);
      throw error;
    }
  };

  // إضافة عميل جديد
  const addCustomer = async (customerData: Omit<Customer, 'id' | 'totalTransactions' | 'totalPurchases' | 'totalPaid' | 'remainingDebt' | 'lastTransaction'>) => {
    try {
      const newCustomerData = {
        ...customerData,
        totalTransactions: 0,
        totalPurchases: 0,
        totalPaid: 0,
        remainingDebt: 0,
        lastTransaction: '',
      };
      await addDoc(collection(db, 'customers'), newCustomerData);
    } catch (error) {
      console.error('Error adding customer:', error);
      throw error;
    }
  };

  // تحديث عميل
  const updateCustomer = async (updatedCustomer: Customer) => {
    try {
      if (updatedCustomer.id) {
        const updateData = {
          name: updatedCustomer.name,
          phone: updatedCustomer.phone,
          address: updatedCustomer.address,
          notes: updatedCustomer.notes,
          totalTransactions: updatedCustomer.totalTransactions,
          totalPurchases: updatedCustomer.totalPurchases,
          totalPaid: updatedCustomer.totalPaid,
          remainingDebt: updatedCustomer.remainingDebt,
          lastTransaction: updatedCustomer.lastTransaction,
        };
        await updateDoc(doc(db, 'customers', updatedCustomer.id), updateData);
      }
    } catch (error) {
      console.error('Error updating customer:', error);
      throw error;
    }
  };

  // حذف عميل
  const deleteCustomer = async (id: string) => {
    try {
      await deleteDoc(doc(db, 'customers', id));
    } catch (error) {
      console.error('Error deleting customer:', error);
      throw error;
    }
  };

  // حساب الإحصائيات
  const getStats = () => {
    const today = new Date().toISOString().split('T')[0];
    const thisMonth = new Date().toISOString().slice(0, 7); // YYYY-MM

    const todaySales = transactions
      .filter(t => t.date === today)
      .reduce((sum, t) => sum + t.totalAmount, 0);

    const thisMonthSales = transactions
      .filter(t => t.date.startsWith(thisMonth))
      .reduce((sum, t) => sum + t.totalAmount, 0);

    const totalDebts = customers.reduce((sum, c) => sum + c.remainingDebt, 0);

    return {
      todaySales,
      totalDebts,
      thisMonthSales,
      customersCount: customers.length,
    };
  };

  const value: DataContextType = {
    transactions,
    customers,
    products,
    addTransaction,
    addProduct,
    updateProduct,
    deleteProduct,
    addCustomer,
    updateCustomer,
    deleteCustomer,
    getStats,
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
};

// Hook لاستخدام السياق
export const useData = () => {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};
