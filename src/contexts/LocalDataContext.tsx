import React, { createContext, useContext, useEffect, useState } from 'react';
import { Capacitor } from '@capacitor/core';
import { databaseService } from '@/services/DatabaseService';
import { syncService } from '@/services/SyncService';
import { 
  Customer, 
  Product, 
  Material, 
  Transaction, 
  StockTransaction, 
  Expense 
} from '@/contexts/DataContext';

interface LocalDataContextType {
  // حالة الاتصال
  isOnline: boolean;
  isLocalDbReady: boolean;
  
  // البيانات المحلية
  localCustomers: Customer[];
  localProducts: Product[];
  localMaterials: Material[];
  localTransactions: Transaction[];
  localStockTransactions: StockTransaction[];
  localExpenses: Expense[];
  
  // العمليات المحلية
  addLocalCustomer: (customer: Omit<Customer, 'id'>) => Promise<string>;
  updateLocalCustomer: (id: string, customer: Partial<Customer>) => Promise<void>;
  deleteLocalCustomer: (id: string) => Promise<void>;
  
  addLocalProduct: (product: Omit<Product, 'id'>) => Promise<string>;
  updateLocalProduct: (id: string, product: Partial<Product>) => Promise<void>;
  deleteLocalProduct: (id: string) => Promise<void>;
  
  addLocalMaterial: (material: Omit<Material, 'id'>) => Promise<string>;
  updateLocalMaterial: (id: string, material: Partial<Material>) => Promise<void>;
  deleteLocalMaterial: (id: string) => Promise<void>;
  
  addLocalTransaction: (transaction: Omit<Transaction, 'id'>) => Promise<string>;
  updateLocalTransaction: (id: string, transaction: Partial<Transaction>) => Promise<void>;
  deleteLocalTransaction: (id: string) => Promise<void>;
  
  addLocalStockTransaction: (stockTransaction: Omit<StockTransaction, 'id'>) => Promise<string>;
  updateLocalStockTransaction: (id: string, stockTransaction: Partial<StockTransaction>) => Promise<void>;
  deleteLocalStockTransaction: (id: string) => Promise<void>;
  
  addLocalExpense: (expense: Omit<Expense, 'id'>) => Promise<string>;
  updateLocalExpense: (id: string, expense: Partial<Expense>) => Promise<void>;
  deleteLocalExpense: (id: string) => Promise<void>;
  
  // المزامنة
  syncNow: () => Promise<void>;
  refreshLocalData: () => Promise<void>;
}

const LocalDataContext = createContext<LocalDataContextType | undefined>(undefined);

export const useLocalData = () => {
  const context = useContext(LocalDataContext);
  if (context === undefined) {
    throw new Error('useLocalData must be used within a LocalDataProvider');
  }
  return context;
};

export const LocalDataProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isLocalDbReady, setIsLocalDbReady] = useState(false);
  
  // البيانات المحلية
  const [localCustomers, setLocalCustomers] = useState<Customer[]>([]);
  const [localProducts, setLocalProducts] = useState<Product[]>([]);
  const [localMaterials, setLocalMaterials] = useState<Material[]>([]);
  const [localTransactions, setLocalTransactions] = useState<Transaction[]>([]);
  const [localStockTransactions, setLocalStockTransactions] = useState<StockTransaction[]>([]);
  const [localExpenses, setLocalExpenses] = useState<Expense[]>([]);

  // تهيئة قاعدة البيانات المحلية
  useEffect(() => {
    const initializeLocalDatabase = async () => {
      try {
        if (Capacitor.isNativePlatform()) {
          await databaseService.initializeDatabase();
          setIsLocalDbReady(true);
          await refreshLocalData();
          console.log('Local database initialized successfully');
        } else {
          console.log('Running on web platform, local database not available');
          setIsLocalDbReady(false);
        }
      } catch (error) {
        console.error('Error initializing local database:', error);
        setIsLocalDbReady(false);
      }
    };

    initializeLocalDatabase();
  }, []);

  // مراقبة حالة الاتصال
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      if (isLocalDbReady) {
        syncNow();
      }
    };
    
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [isLocalDbReady]);

  // تحديث البيانات المحلية
  const refreshLocalData = async () => {
    if (!isLocalDbReady) return;

    try {
      // تحميل العملاء
      const customersResult = await databaseService.executeQuery('SELECT * FROM customers ORDER BY name');
      if (customersResult.values) {
        const customers = customersResult.values.map((row: any[]) => 
          convertSQLiteRowToObject(row, customersResult.columns) as Customer
        );
        setLocalCustomers(customers);
      }

      // تحميل المنتجات
      const productsResult = await databaseService.executeQuery('SELECT * FROM products ORDER BY name');
      if (productsResult.values) {
        const products = productsResult.values.map((row: any[]) => {
          const product = convertSQLiteRowToObject(row, productsResult.columns) as Product;
          if (product.materials && typeof product.materials === 'string') {
            product.materials = JSON.parse(product.materials);
          }
          return product;
        });
        setLocalProducts(products);
      }

      // تحميل الخامات
      const materialsResult = await databaseService.executeQuery('SELECT * FROM materials ORDER BY name');
      if (materialsResult.values) {
        const materials = materialsResult.values.map((row: any[]) => 
          convertSQLiteRowToObject(row, materialsResult.columns) as Material
        );
        setLocalMaterials(materials);
      }

      // تحميل المعاملات
      const transactionsResult = await databaseService.executeQuery('SELECT * FROM transactions ORDER BY date DESC, time DESC');
      if (transactionsResult.values) {
        const transactions = transactionsResult.values.map((row: any[]) => 
          convertSQLiteRowToObject(row, transactionsResult.columns) as Transaction
        );
        setLocalTransactions(transactions);
      }

      // تحميل حركات المخزون
      const stockTransactionsResult = await databaseService.executeQuery('SELECT * FROM stock_transactions ORDER BY date DESC, time DESC');
      if (stockTransactionsResult.values) {
        const stockTransactions = stockTransactionsResult.values.map((row: any[]) => 
          convertSQLiteRowToObject(row, stockTransactionsResult.columns) as StockTransaction
        );
        setLocalStockTransactions(stockTransactions);
      }

      // تحميل المصروفات
      const expensesResult = await databaseService.executeQuery('SELECT * FROM expenses ORDER BY date DESC, time DESC');
      if (expensesResult.values) {
        const expenses = expensesResult.values.map((row: any[]) => {
          const expense = convertSQLiteRowToObject(row, expensesResult.columns) as Expense;
          if (expense.attachments && typeof expense.attachments === 'string') {
            expense.attachments = JSON.parse(expense.attachments);
          }
          return expense;
        });
        setLocalExpenses(expenses);
      }

      console.log('Local data refreshed successfully');
    } catch (error) {
      console.error('Error refreshing local data:', error);
    }
  };

  // دالة مساعدة لتحويل صف SQLite إلى كائن
  const convertSQLiteRowToObject = (row: any[], columns: string[]): any => {
    const obj: any = {};
    columns.forEach((column, index) => {
      obj[column] = row[index];
    });
    return obj;
  };

  // دالة مساعدة لإنشاء ID فريد
  const generateId = (): string => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  };

  // العمليات المحلية للعملاء
  const addLocalCustomer = async (customerData: Omit<Customer, 'id'>): Promise<string> => {
    if (!isLocalDbReady) throw new Error('Local database not ready');
    
    const id = generateId();
    const now = new Date().toISOString();
    const customer: Customer = {
      ...customerData,
      id
    };

    await databaseService.executeRun(
      'INSERT INTO customers (id, name, phone, email, address, created_at, updated_at, synced) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
      [id, customer.name, customer.phone || '', customer.email || '', customer.address || '', now, now, 0]
    );

    await databaseService.addSyncLog('customers', id, 'create');
    await refreshLocalData();
    
    if (isOnline) {
      syncService.syncToFirebase();
    }
    
    return id;
  };

  const updateLocalCustomer = async (id: string, customerData: Partial<Customer>): Promise<void> => {
    if (!isLocalDbReady) throw new Error('Local database not ready');
    
    const now = new Date().toISOString();
    const updateFields = Object.keys(customerData).filter(key => key !== 'id');
    const updateValues = updateFields.map(field => (customerData as any)[field]);
    
    const updateSQL = `UPDATE customers SET ${updateFields.map(field => `${field} = ?`).join(', ')}, updated_at = ?, synced = ? WHERE id = ?`;
    
    await databaseService.executeRun(updateSQL, [...updateValues, now, 0, id]);
    await databaseService.addSyncLog('customers', id, 'update');
    await refreshLocalData();
    
    if (isOnline) {
      syncService.syncToFirebase();
    }
  };

  const deleteLocalCustomer = async (id: string): Promise<void> => {
    if (!isLocalDbReady) throw new Error('Local database not ready');
    
    await databaseService.executeRun('DELETE FROM customers WHERE id = ?', [id]);
    await databaseService.addSyncLog('customers', id, 'delete');
    await refreshLocalData();
    
    if (isOnline) {
      syncService.syncToFirebase();
    }
  };

  // المزامنة
  const syncNow = async (): Promise<void> => {
    if (!isLocalDbReady || !isOnline) return;
    
    try {
      await syncService.forceSyncNow();
      await refreshLocalData();
    } catch (error) {
      console.error('Error during sync:', error);
    }
  };

  // عمليات المنتجات
  const addLocalProduct = async (productData: Omit<Product, 'id'>): Promise<string> => {
    if (!isLocalDbReady) throw new Error('Local database not ready');

    const id = Date.now().toString();
    const materialsJson = JSON.stringify(productData.materials || []);

    await databaseService.executeRun(
      `INSERT INTO products (id, name, price, unit, category, materials, created_at, updated_at, synced)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [id, productData.name, productData.price, productData.unit, productData.category,
       materialsJson, new Date().toISOString(), new Date().toISOString(), 0]
    );

    await databaseService.addSyncLog('products', id, 'create');
    await refreshLocalData();

    if (isOnline) {
      syncService.syncToFirebase();
    }

    return id;
  };

  const updateLocalProduct = async (id: string, productData: Partial<Product>): Promise<void> => {
    if (!isLocalDbReady) throw new Error('Local database not ready');

    const updates = [];
    const values = [];

    if (productData.name) { updates.push('name = ?'); values.push(productData.name); }
    if (productData.price !== undefined) { updates.push('price = ?'); values.push(productData.price); }
    if (productData.unit) { updates.push('unit = ?'); values.push(productData.unit); }
    if (productData.category) { updates.push('category = ?'); values.push(productData.category); }
    if (productData.materials) {
      updates.push('materials = ?');
      values.push(JSON.stringify(productData.materials));
    }

    updates.push('updated_at = ?', 'synced = ?');
    values.push(new Date().toISOString(), 0, id);

    await databaseService.executeRun(
      `UPDATE products SET ${updates.join(', ')} WHERE id = ?`,
      values
    );

    await databaseService.addSyncLog('products', id, 'update');
    await refreshLocalData();

    if (isOnline) {
      syncService.syncToFirebase();
    }
  };

  const deleteLocalProduct = async (id: string): Promise<void> => {
    if (!isLocalDbReady) throw new Error('Local database not ready');

    await databaseService.executeRun('DELETE FROM products WHERE id = ?', [id]);
    await databaseService.addSyncLog('products', id, 'delete');
    await refreshLocalData();

    if (isOnline) {
      syncService.syncToFirebase();
    }
  };

  const addLocalMaterial = async (materialData: Omit<Material, 'id'>): Promise<string> => {
    if (!isLocalDbReady) throw new Error('Local database not ready');

    const id = Date.now().toString();

    await databaseService.executeRun(
      `INSERT INTO materials (id, name, unit, cost_per_unit, available_quantity, supplier,
       storage_location, category, created_at, updated_at, synced)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [id, materialData.name, materialData.unit, materialData.costPerUnit,
       materialData.availableQuantity, materialData.supplier, materialData.storageLocation,
       materialData.category, new Date().toISOString(), new Date().toISOString(), 0]
    );

    await databaseService.addSyncLog('materials', id, 'create');
    await refreshLocalData();

    if (isOnline) {
      syncService.syncToFirebase();
    }

    return id;
  };

  const updateLocalMaterial = async (id: string, materialData: Partial<Material>): Promise<void> => {
    if (!isLocalDbReady) throw new Error('Local database not ready');

    const updates = [];
    const values = [];

    if (materialData.name) { updates.push('name = ?'); values.push(materialData.name); }
    if (materialData.unit) { updates.push('unit = ?'); values.push(materialData.unit); }
    if (materialData.costPerUnit !== undefined) { updates.push('cost_per_unit = ?'); values.push(materialData.costPerUnit); }
    if (materialData.availableQuantity !== undefined) { updates.push('available_quantity = ?'); values.push(materialData.availableQuantity); }
    if (materialData.supplier) { updates.push('supplier = ?'); values.push(materialData.supplier); }
    if (materialData.storageLocation) { updates.push('storage_location = ?'); values.push(materialData.storageLocation); }
    if (materialData.category) { updates.push('category = ?'); values.push(materialData.category); }

    updates.push('updated_at = ?', 'synced = ?');
    values.push(new Date().toISOString(), 0, id);

    await databaseService.executeRun(
      `UPDATE materials SET ${updates.join(', ')} WHERE id = ?`,
      values
    );

    await databaseService.addSyncLog('materials', id, 'update');
    await refreshLocalData();

    if (isOnline) {
      syncService.syncToFirebase();
    }
  };

  const deleteLocalMaterial = async (id: string): Promise<void> => {
    if (!isLocalDbReady) throw new Error('Local database not ready');

    await databaseService.executeRun('DELETE FROM materials WHERE id = ?', [id]);
    await databaseService.addSyncLog('materials', id, 'delete');
    await refreshLocalData();

    if (isOnline) {
      syncService.syncToFirebase();
    }
  };

  const addLocalTransaction = async (transactionData: Omit<Transaction, 'id'>): Promise<string> => {
    // سيتم تطبيقها
    return '';
  };

  const updateLocalTransaction = async (id: string, transactionData: Partial<Transaction>): Promise<void> => {
    // سيتم تطبيقها
  };

  const deleteLocalTransaction = async (id: string): Promise<void> => {
    // سيتم تطبيقها
  };

  const addLocalStockTransaction = async (stockTransactionData: Omit<StockTransaction, 'id'>): Promise<string> => {
    // سيتم تطبيقها
    return '';
  };

  const updateLocalStockTransaction = async (id: string, stockTransactionData: Partial<StockTransaction>): Promise<void> => {
    // سيتم تطبيقها
  };

  const deleteLocalStockTransaction = async (id: string): Promise<void> => {
    // سيتم تطبيقها
  };

  const addLocalExpense = async (expenseData: Omit<Expense, 'id'>): Promise<string> => {
    // سيتم تطبيقها
    return '';
  };

  const updateLocalExpense = async (id: string, expenseData: Partial<Expense>): Promise<void> => {
    // سيتم تطبيقها
  };

  const deleteLocalExpense = async (id: string): Promise<void> => {
    // سيتم تطبيقها
  };

  const value: LocalDataContextType = {
    isOnline,
    isLocalDbReady,
    localCustomers,
    localProducts,
    localMaterials,
    localTransactions,
    localStockTransactions,
    localExpenses,
    addLocalCustomer,
    updateLocalCustomer,
    deleteLocalCustomer,
    addLocalProduct,
    updateLocalProduct,
    deleteLocalProduct,
    addLocalMaterial,
    updateLocalMaterial,
    deleteLocalMaterial,
    addLocalTransaction,
    updateLocalTransaction,
    deleteLocalTransaction,
    addLocalStockTransaction,
    updateLocalStockTransaction,
    deleteLocalStockTransaction,
    addLocalExpense,
    updateLocalExpense,
    deleteLocalExpense,
    syncNow,
    refreshLocalData
  };

  return (
    <LocalDataContext.Provider value={value}>
      {children}
    </LocalDataContext.Provider>
  );
};
