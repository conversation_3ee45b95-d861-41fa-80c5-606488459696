import { useState, useEffect } from 'react';
import { Capacitor } from '@capacitor/core';
import { databaseService } from '@/services/DatabaseService';
import { syncService } from '@/services/SyncService';

export interface LocalDatabaseStatus {
  isReady: boolean;
  isOnline: boolean;
  lastSyncTime: Date | null;
  error: string | null;
}

export const useLocalDatabase = () => {
  const [status, setStatus] = useState<LocalDatabaseStatus>({
    isReady: false,
    isOnline: navigator.onLine,
    lastSyncTime: null,
    error: null
  });

  useEffect(() => {
    const initializeDatabase = async () => {
      try {
        if (Capacitor.isNativePlatform()) {
          await databaseService.initializeDatabase();
          setStatus(prev => ({ 
            ...prev, 
            isReady: true, 
            error: null 
          }));
          
          // بدء المزامنة الأولية
          if (navigator.onLine) {
            await syncService.syncFromFirebase();
            setStatus(prev => ({ 
              ...prev, 
              lastSyncTime: new Date() 
            }));
          }
        } else {
          console.log('Running on web platform, using Firebase directly');
          setStatus(prev => ({ 
            ...prev, 
            isReady: true 
          }));
        }
      } catch (error) {
        console.error('Error initializing local database:', error);
        setStatus(prev => ({ 
          ...prev, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        }));
      }
    };

    initializeDatabase();

    // مراقبة حالة الاتصال
    const handleOnline = () => {
      setStatus(prev => ({ ...prev, isOnline: true }));
      if (status.isReady && Capacitor.isNativePlatform()) {
        syncService.forceSyncNow().then(() => {
          setStatus(prev => ({ ...prev, lastSyncTime: new Date() }));
        });
      }
    };

    const handleOffline = () => {
      setStatus(prev => ({ ...prev, isOnline: false }));
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const forceSync = async () => {
    if (!status.isReady || !status.isOnline) return;
    
    try {
      await syncService.forceSyncNow();
      setStatus(prev => ({ 
        ...prev, 
        lastSyncTime: new Date(),
        error: null 
      }));
    } catch (error) {
      console.error('Error during force sync:', error);
      setStatus(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Sync error' 
      }));
    }
  };

  return {
    ...status,
    forceSync,
    isNativePlatform: Capacitor.isNativePlatform()
  };
};
