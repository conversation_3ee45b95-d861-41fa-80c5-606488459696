@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* تعريف نظام التصميم - برنامج المحاسبة اليومي */

@layer base {
  :root {
    /* الألوان الأساسية - نظام أخضر وأزرق مهني */
    --background: 210 25% 98%;
    --foreground: 210 25% 8%;

    --card: 0 0% 100%;
    --card-foreground: 210 25% 12%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 25% 8%;

    /* أخضر مهني للمالية */
    --primary: 158 64% 25%;
    --primary-foreground: 0 0% 98%;
    --primary-light: 158 45% 45%;
    --primary-glow: 158 45% 55%;

    /* أزرق ثانوي */
    --secondary: 210 40% 92%;
    --secondary-foreground: 210 25% 12%;
    --secondary-accent: 210 100% 45%;

    --muted: 210 25% 95%;
    --muted-foreground: 210 15% 55%;

    --accent: 210 25% 95%;
    --accent-foreground: 210 25% 12%;

    /* أحمر للديون والتحذيرات */
    --destructive: 0 75% 55%;
    --destructive-foreground: 0 0% 98%;

    /* أخضر للأرباح */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;

    /* برتقالي للتنبيهات */
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;

    --border: 210 25% 88%;
    --input: 210 25% 92%;
    --ring: 158 64% 25%;

    /* التدرجات */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-light)));
    --gradient-secondary: linear-gradient(135deg, hsl(var(--secondary-accent)), hsl(210 80% 55%));
    --gradient-success: linear-gradient(135deg, hsl(var(--success)), hsl(142 60% 45%));
    --gradient-background: linear-gradient(180deg, hsl(var(--background)), hsl(210 20% 96%));

    /* الظلال */
    --shadow-primary: 0 10px 25px hsl(var(--primary) / 0.15);
    --shadow-card: 0 4px 12px hsl(210 15% 15% / 0.08);
    --shadow-elevated: 0 8px 32px hsl(210 15% 15% / 0.12);

    /* الانتقالات */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* خلفيات داكنة أنيقة */
    --background: 222 47% 6%;
    --foreground: 210 40% 98%;

    --card: 222 47% 8%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 8%;
    --popover-foreground: 210 40% 98%;

    /* أخضر مهني للوضع الداكن */
    --primary: 158 64% 35%;
    --primary-foreground: 0 0% 98%;
    --primary-light: 158 45% 55%;
    --primary-glow: 158 45% 65%;

    /* أزرق ثانوي للوضع الداكن */
    --secondary: 217 32% 20%;
    --secondary-foreground: 210 40% 98%;
    --secondary-accent: 210 100% 55%;

    --muted: 217 32% 15%;
    --muted-foreground: 215 20% 65%;

    --accent: 217 32% 20%;
    --accent-foreground: 210 40% 98%;

    /* أحمر للديون في الوضع الداكن */
    --destructive: 0 75% 45%;
    --destructive-foreground: 210 40% 98%;

    /* أخضر للأرباح في الوضع الداكن */
    --success: 142 76% 46%;
    --success-foreground: 0 0% 98%;

    /* برتقالي للتنبيهات في الوضع الداكن */
    --warning: 38 92% 60%;
    --warning-foreground: 0 0% 98%;

    --border: 217 32% 20%;
    --input: 217 32% 18%;
    --ring: 158 64% 35%;

    /* التدرجات للوضع الداكن */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-light)));
    --gradient-secondary: linear-gradient(135deg, hsl(var(--secondary-accent)), hsl(210 80% 65%));
    --gradient-success: linear-gradient(135deg, hsl(var(--success)), hsl(142 60% 55%));
    --gradient-background: linear-gradient(180deg, hsl(var(--background)), hsl(222 40% 8%));

    /* الظلال للوضع الداكن */
    --shadow-primary: 0 10px 25px hsl(var(--primary) / 0.25);
    --shadow-card: 0 4px 12px hsl(0 0% 0% / 0.25);
    --shadow-elevated: 0 8px 32px hsl(0 0% 0% / 0.35);

    /* الشريط الجانبي */
    --sidebar-background: 222 47% 5%;
    --sidebar-foreground: 240 5% 96%;
    --sidebar-primary: 158 64% 35%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 217 32% 12%;
    --sidebar-accent-foreground: 240 5% 96%;
    --sidebar-border: 217 32% 15%;
    --sidebar-ring: 158 64% 35%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-arabic;
    background: var(--gradient-background);
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
  }

  /* عناصر مخصصة للتطبيق العربي */
  .font-arabic {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }

  .gradient-primary {
    background: var(--gradient-primary);
  }

  .gradient-secondary {
    background: var(--gradient-secondary);
  }

  .gradient-success {
    background: var(--gradient-success);
  }

  .shadow-primary {
    box-shadow: var(--shadow-primary);
  }

  .shadow-card {
    box-shadow: var(--shadow-card);
  }

  .shadow-elevated {
    box-shadow: var(--shadow-elevated);
  }

  .transition-smooth {
    transition: var(--transition-smooth);
  }

  .transition-bounce {
    transition: var(--transition-bounce);
  }

  /* تحسينات للجداول العربية */
  .table-rtl {
    direction: rtl;
  }

  .table-rtl th {
    text-align: right;
  }

  .table-rtl td {
    text-align: right;
  }

  /* تحسينات للأرقام */
  .numbers-ltr {
    direction: ltr;
    unicode-bidi: embed;
  }
}