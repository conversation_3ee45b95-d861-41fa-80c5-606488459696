import { databaseService } from './DatabaseService';
import { db } from '@/lib/firebase';
import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  query, 
  where, 
  orderBy,
  Timestamp 
} from 'firebase/firestore';

export interface SyncService {
  syncToFirebase(): Promise<void>;
  syncFromFirebase(): Promise<void>;
  enableAutoSync(): void;
  disableAutoSync(): void;
}

class FirebaseSyncService implements SyncService {
  private autoSyncInterval: NodeJS.Timeout | null = null;
  private readonly syncIntervalMs = 30000; // 30 ثانية

  constructor() {
    // بدء المزامنة التلقائية عند إنشاء الخدمة
    this.enableAutoSync();
  }

  async syncToFirebase(): Promise<void> {
    try {
      if (!(await databaseService.isOnline())) {
        console.log('Device is offline, skipping sync to Firebase');
        return;
      }

      console.log('Starting sync to Firebase...');
      
      // مزامنة البيانات غير المتزامنة
      await this.syncTableToFirebase('customers');
      await this.syncTableToFirebase('products');
      await this.syncTableToFirebase('materials');
      await this.syncTableToFirebase('transactions');
      await this.syncTableToFirebase('stock_transactions');
      await this.syncTableToFirebase('expenses');

      console.log('Sync to Firebase completed successfully');
    } catch (error) {
      console.error('Error syncing to Firebase:', error);
    }
  }

  async syncFromFirebase(): Promise<void> {
    try {
      if (!(await databaseService.isOnline())) {
        console.log('Device is offline, skipping sync from Firebase');
        return;
      }

      console.log('Starting sync from Firebase...');
      
      // مزامنة البيانات من Firebase إلى قاعدة البيانات المحلية
      await this.syncTableFromFirebase('customers');
      await this.syncTableFromFirebase('products');
      await this.syncTableFromFirebase('materials');
      await this.syncTableFromFirebase('transactions');
      await this.syncTableFromFirebase('stock_transactions');
      await this.syncTableFromFirebase('expenses');

      console.log('Sync from Firebase completed successfully');
    } catch (error) {
      console.error('Error syncing from Firebase:', error);
    }
  }

  private async syncTableToFirebase(tableName: string): Promise<void> {
    try {
      // الحصول على السجلات غير المتزامنة
      const unsyncedRecords = await databaseService.executeQuery(
        `SELECT * FROM ${tableName} WHERE synced = 0`
      );

      if (!unsyncedRecords.values || unsyncedRecords.values.length === 0) {
        return;
      }

      for (const record of unsyncedRecords.values) {
        try {
          // تحويل السجل إلى كائن
          const recordData = this.convertSQLiteRowToObject(record, unsyncedRecords.columns);
          
          // إزالة الحقول المحلية
          delete recordData.synced;
          delete recordData.created_at;
          delete recordData.updated_at;

          // تحويل JSON strings إلى objects
          if (recordData.materials && typeof recordData.materials === 'string') {
            recordData.materials = JSON.parse(recordData.materials);
          }
          if (recordData.attachments && typeof recordData.attachments === 'string') {
            recordData.attachments = JSON.parse(recordData.attachments);
          }

          // رفع البيانات إلى Firebase
          const firebaseCollection = collection(db, tableName);
          
          if (recordData.id) {
            // تحديث سجل موجود
            await updateDoc(doc(db, tableName, recordData.id), recordData);
          } else {
            // إضافة سجل جديد
            const docRef = await addDoc(firebaseCollection, recordData);
            recordData.id = docRef.id;
          }

          // تحديث حالة المزامنة في قاعدة البيانات المحلية
          await databaseService.executeRun(
            `UPDATE ${tableName} SET synced = 1 WHERE id = ?`,
            [recordData.id]
          );

          console.log(`Synced ${tableName} record ${recordData.id} to Firebase`);
        } catch (recordError) {
          console.error(`Error syncing ${tableName} record:`, recordError);
        }
      }
    } catch (error) {
      console.error(`Error syncing ${tableName} to Firebase:`, error);
    }
  }

  private async syncTableFromFirebase(tableName: string): Promise<void> {
    try {
      // الحصول على البيانات من Firebase
      const firebaseCollection = collection(db, tableName);
      const snapshot = await getDocs(firebaseCollection);

      for (const docSnapshot of snapshot.docs) {
        const firebaseData = { id: docSnapshot.id, ...docSnapshot.data() };
        
        try {
          // التحقق من وجود السجل في قاعدة البيانات المحلية
          const existingRecord = await databaseService.executeQuery(
            `SELECT id FROM ${tableName} WHERE id = ?`,
            [firebaseData.id]
          );

          // تحويل objects إلى JSON strings
          if (firebaseData.materials && typeof firebaseData.materials === 'object') {
            firebaseData.materials = JSON.stringify(firebaseData.materials);
          }
          if (firebaseData.attachments && typeof firebaseData.attachments === 'object') {
            firebaseData.attachments = JSON.stringify(firebaseData.attachments);
          }

          // إضافة timestamps
          const now = new Date().toISOString();
          firebaseData.created_at = firebaseData.created_at || now;
          firebaseData.updated_at = now;
          firebaseData.synced = 1;

          if (existingRecord.values && existingRecord.values.length > 0) {
            // تحديث السجل الموجود
            const updateFields = Object.keys(firebaseData).filter(key => key !== 'id');
            const updateValues = updateFields.map(field => firebaseData[field]);
            const updateSQL = `UPDATE ${tableName} SET ${updateFields.map(field => `${field} = ?`).join(', ')} WHERE id = ?`;
            
            await databaseService.executeRun(updateSQL, [...updateValues, firebaseData.id]);
          } else {
            // إدراج سجل جديد
            const insertFields = Object.keys(firebaseData);
            const insertValues = insertFields.map(field => firebaseData[field]);
            const insertSQL = `INSERT INTO ${tableName} (${insertFields.join(', ')}) VALUES (${insertFields.map(() => '?').join(', ')})`;
            
            await databaseService.executeRun(insertSQL, insertValues);
          }

          console.log(`Synced ${tableName} record ${firebaseData.id} from Firebase`);
        } catch (recordError) {
          console.error(`Error syncing ${tableName} record from Firebase:`, recordError);
        }
      }
    } catch (error) {
      console.error(`Error syncing ${tableName} from Firebase:`, error);
    }
  }

  private convertSQLiteRowToObject(row: any[], columns: string[]): any {
    const obj: any = {};
    columns.forEach((column, index) => {
      obj[column] = row[index];
    });
    return obj;
  }

  enableAutoSync(): void {
    if (this.autoSyncInterval) {
      clearInterval(this.autoSyncInterval);
    }

    this.autoSyncInterval = setInterval(async () => {
      try {
        await this.syncToFirebase();
        await this.syncFromFirebase();
      } catch (error) {
        console.error('Auto sync error:', error);
      }
    }, this.syncIntervalMs);

    console.log('Auto sync enabled');
  }

  disableAutoSync(): void {
    if (this.autoSyncInterval) {
      clearInterval(this.autoSyncInterval);
      this.autoSyncInterval = null;
    }
    console.log('Auto sync disabled');
  }

  async forceSyncNow(): Promise<void> {
    console.log('Force sync initiated...');
    await this.syncToFirebase();
    await this.syncFromFirebase();
    console.log('Force sync completed');
  }
}

// إنشاء مثيل واحد من خدمة المزامنة
export const syncService = new FirebaseSyncService();
