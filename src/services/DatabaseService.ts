import { CapacitorSQLite, SQLiteConnection, SQLiteDBConnection } from '@capacitor-community/sqlite';
import { Capacitor } from '@capacitor/core';

export interface DatabaseService {
  initializeDatabase(): Promise<void>;
  getConnection(): Promise<SQLiteDBConnection>;
  closeConnection(): Promise<void>;
  syncWithFirebase(): Promise<void>;
}

class LocalDatabaseService implements DatabaseService {
  private sqlite: SQLiteConnection;
  private db: SQLiteDBConnection | null = null;
  private readonly dbName = 'yaumi_account_book.db';
  private readonly dbVersion = 1;

  constructor() {
    this.sqlite = new SQLiteConnection(CapacitorSQLite);
  }

  async initializeDatabase(): Promise<void> {
    try {
      // التحقق من دعم المنصة
      if (!Capacitor.isNativePlatform()) {
        console.warn('SQLite is not supported on web platform');
        return;
      }

      // إنشاء الاتصال بقاعدة البيانات
      const ret = await this.sqlite.checkConnectionsConsistency();
      const isConn = (await this.sqlite.isConnection(this.dbName, false)).result;

      if (ret.result && isConn) {
        this.db = await this.sqlite.retrieveConnection(this.dbName, false);
      } else {
        this.db = await this.sqlite.createConnection(
          this.dbName,
          false,
          'no-encryption',
          this.dbVersion,
          false
        );
      }

      await this.db.open();
      await this.createTables();
      
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Error initializing database:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const createTablesSQL = `
      -- جدول العملاء
      CREATE TABLE IF NOT EXISTS customers (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        phone TEXT,
        email TEXT,
        address TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        synced INTEGER DEFAULT 0
      );

      -- جدول المنتجات
      CREATE TABLE IF NOT EXISTS products (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        price REAL NOT NULL,
        category TEXT,
        description TEXT,
        materials TEXT, -- JSON string for materials array
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        synced INTEGER DEFAULT 0
      );

      -- جدول الخامات
      CREATE TABLE IF NOT EXISTS materials (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        unit TEXT NOT NULL,
        cost_per_unit REAL NOT NULL,
        available_quantity REAL NOT NULL,
        supplier TEXT,
        storage_location TEXT,
        category TEXT,
        description TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        synced INTEGER DEFAULT 0
      );

      -- جدول المعاملات
      CREATE TABLE IF NOT EXISTS transactions (
        id TEXT PRIMARY KEY,
        customer_id TEXT NOT NULL,
        product_id TEXT NOT NULL,
        quantity REAL NOT NULL,
        unit_price REAL NOT NULL,
        total_amount REAL NOT NULL,
        remaining_amount REAL NOT NULL,
        date TEXT NOT NULL,
        time TEXT NOT NULL,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        synced INTEGER DEFAULT 0,
        FOREIGN KEY (customer_id) REFERENCES customers (id),
        FOREIGN KEY (product_id) REFERENCES products (id)
      );

      -- جدول حركات المخزون
      CREATE TABLE IF NOT EXISTS stock_transactions (
        id TEXT PRIMARY KEY,
        material_id TEXT NOT NULL,
        type TEXT NOT NULL, -- 'purchase' or 'usage'
        quantity REAL NOT NULL,
        unit_cost REAL,
        total_cost REAL,
        supplier TEXT,
        reference_id TEXT,
        reference_type TEXT,
        date TEXT NOT NULL,
        time TEXT NOT NULL,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        synced INTEGER DEFAULT 0,
        FOREIGN KEY (material_id) REFERENCES materials (id)
      );

      -- جدول المصروفات
      CREATE TABLE IF NOT EXISTS expenses (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        category TEXT NOT NULL,
        amount REAL NOT NULL,
        description TEXT,
        recipient TEXT,
        payment_method TEXT NOT NULL,
        date TEXT NOT NULL,
        time TEXT NOT NULL,
        status TEXT NOT NULL,
        is_advance INTEGER DEFAULT 0,
        advance_return_date TEXT,
        advance_returned INTEGER DEFAULT 0,
        attachments TEXT, -- JSON string for attachments array
        approved_by TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        synced INTEGER DEFAULT 0
      );

      -- جدول المزامنة
      CREATE TABLE IF NOT EXISTS sync_log (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        table_name TEXT NOT NULL,
        record_id TEXT NOT NULL,
        action TEXT NOT NULL, -- 'create', 'update', 'delete'
        timestamp TEXT NOT NULL,
        synced INTEGER DEFAULT 0
      );

      -- إنشاء الفهارس لتحسين الأداء
      CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name);
      CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);
      CREATE INDEX IF NOT EXISTS idx_materials_name ON materials(name);
      CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(date);
      CREATE INDEX IF NOT EXISTS idx_transactions_customer ON transactions(customer_id);
      CREATE INDEX IF NOT EXISTS idx_stock_transactions_date ON stock_transactions(date);
      CREATE INDEX IF NOT EXISTS idx_stock_transactions_material ON stock_transactions(material_id);
      CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(date);
      CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category);
      CREATE INDEX IF NOT EXISTS idx_sync_log_table ON sync_log(table_name);
      CREATE INDEX IF NOT EXISTS idx_sync_log_synced ON sync_log(synced);
    `;

    await this.db.execute(createTablesSQL);
    console.log('Database tables created successfully');
  }

  async getConnection(): Promise<SQLiteDBConnection> {
    if (!this.db) {
      await this.initializeDatabase();
    }
    if (!this.db) {
      throw new Error('Failed to initialize database connection');
    }
    return this.db;
  }

  async closeConnection(): Promise<void> {
    if (this.db) {
      await this.db.close();
      await this.sqlite.closeConnection(this.dbName, false);
      this.db = null;
    }
  }

  async syncWithFirebase(): Promise<void> {
    // سيتم تطبيق منطق المزامنة مع Firebase هنا
    console.log('Sync with Firebase - to be implemented');
  }

  // دوال مساعدة للعمليات الأساسية
  async executeQuery(query: string, values?: any[]): Promise<any> {
    const db = await this.getConnection();
    return await db.query(query, values);
  }

  async executeRun(query: string, values?: any[]): Promise<any> {
    const db = await this.getConnection();
    return await db.run(query, values);
  }

  async isOnline(): Promise<boolean> {
    return navigator.onLine;
  }

  async addSyncLog(tableName: string, recordId: string, action: string): Promise<void> {
    const timestamp = new Date().toISOString();
    await this.executeRun(
      'INSERT INTO sync_log (table_name, record_id, action, timestamp) VALUES (?, ?, ?, ?)',
      [tableName, recordId, action, timestamp]
    );
  }
}

// إنشاء مثيل واحد من الخدمة
export const databaseService = new LocalDatabaseService();
