/* تحسينات التجاوب للهاتف المحمول */

/* إضافة breakpoint جديد للشاشات الصغيرة جداً */
@media (min-width: 475px) {
  .xs\:inline {
    display: inline;
  }
  
  .xs\:block {
    display: block;
  }
  
  .xs\:flex {
    display: flex;
  }
  
  .xs\:grid {
    display: grid;
  }
  
  .xs\:hidden {
    display: none;
  }
}

/* تحسينات للجداول على الهاتف المحمول */
@media (max-width: 640px) {
  .table-responsive {
    font-size: 0.75rem;
  }
  
  .table-responsive th,
  .table-responsive td {
    padding: 0.5rem 0.25rem;
    white-space: nowrap;
  }
  
  .table-responsive .numbers-ltr {
    font-size: 0.7rem;
  }
}

/* تحسينات للبطاقات على الهاتف المحمول */
@media (max-width: 640px) {
  .card-mobile {
    padding: 1rem;
    margin: 0.5rem 0;
  }
  
  .card-mobile h3 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }
  
  .card-mobile .text-2xl {
    font-size: 1.25rem;
  }
}

/* تحسينات للنماذج على الهاتف المحمول */
@media (max-width: 640px) {
  .form-mobile input,
  .form-mobile select,
  .form-mobile textarea {
    font-size: 16px; /* منع التكبير التلقائي في iOS */
  }
  
  .form-mobile .grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* تحسينات للأزرار على الهاتف المحمول */
@media (max-width: 640px) {
  .btn-mobile {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    min-height: 44px; /* الحد الأدنى للمس على iOS */
  }
  
  .btn-mobile svg {
    width: 1rem;
    height: 1rem;
  }
}

/* تحسينات للتنقل على الهاتف المحمول */
@media (max-width: 640px) {
  .nav-mobile {
    padding: 0.5rem;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .nav-mobile::-webkit-scrollbar {
    display: none;
  }
  
  .nav-mobile {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

/* تحسينات للنصوص العربية */
.rtl-text {
  direction: rtl;
  text-align: right;
}

.ltr-numbers {
  direction: ltr;
  display: inline-block;
}

/* تحسينات للتباعد على الشاشات الصغيرة */
@media (max-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .space-y-mobile > * + * {
    margin-top: 1rem;
  }
  
  .gap-mobile {
    gap: 0.75rem;
  }
}

/* تحسينات للصور على الهاتف المحمول */
@media (max-width: 640px) {
  .hero-image {
    height: 200px;
    object-fit: cover;
  }
  
  .hero-content {
    padding: 1rem;
  }
  
  .hero-content h2 {
    font-size: 1.25rem;
    line-height: 1.4;
  }
  
  .hero-content p {
    font-size: 0.875rem;
    line-height: 1.4;
  }
}

/* تحسينات للتمرير الأفقي */
.scroll-container {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.scroll-container::-webkit-scrollbar {
  height: 4px;
}

.scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.scroll-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.scroll-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* تحسينات للتركيز والوصولية */
@media (max-width: 640px) {
  button:focus,
  input:focus,
  select:focus,
  textarea:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }
}

/* تحسينات للطباعة */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-full-width {
    width: 100% !important;
  }
  
  .print-break-after {
    page-break-after: always;
  }
}

/* تحسينات للوضع المظلم على الهاتف المحمول */
@media (max-width: 640px) and (prefers-color-scheme: dark) {
  .mobile-dark {
    background-color: #1f2937;
    color: #f9fafb;
  }
}

/* تحسينات للحركة والانتقالات */
@media (prefers-reduced-motion: no-preference) {
  .smooth-transition {
    transition: all 0.2s ease-in-out;
  }
}

@media (prefers-reduced-motion: reduce) {
  .smooth-transition {
    transition: none;
  }
}

/* تحسينات للشاشات عالية الكثافة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .high-dpi-text {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}
