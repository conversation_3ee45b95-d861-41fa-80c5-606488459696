import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useLocalDatabase } from '@/hooks/useLocalDatabase';
import { Wifi, WifiOff, Database, Sync, AlertCircle } from 'lucide-react';

const DatabaseStatus: React.FC = () => {
  const { 
    isReady, 
    isOnline, 
    lastSyncTime, 
    error, 
    forceSync, 
    isNativePlatform 
  } = useLocalDatabase();

  if (!isNativePlatform) {
    return (
      <Card className="w-full max-w-md">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-sm">
            <Database className="h-4 w-4" />
            حالة قاعدة البيانات
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="flex items-center gap-1">
              <Wifi className="h-3 w-3" />
              متصل بالإنترنت - Firebase
            </Badge>
          </div>
          <p className="text-xs text-muted-foreground mt-2">
            يعمل التطبيق على المتصفح مع Firebase مباشرة
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          <Database className="h-4 w-4" />
          حالة قاعدة البيانات المحلية
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* حالة قاعدة البيانات المحلية */}
        <div className="flex items-center justify-between">
          <span className="text-sm">قاعدة البيانات المحلية:</span>
          <Badge variant={isReady ? "default" : "destructive"}>
            {isReady ? "جاهزة" : "غير جاهزة"}
          </Badge>
        </div>

        {/* حالة الاتصال */}
        <div className="flex items-center justify-between">
          <span className="text-sm">الاتصال بالإنترنت:</span>
          <Badge variant={isOnline ? "default" : "secondary"} className="flex items-center gap-1">
            {isOnline ? <Wifi className="h-3 w-3" /> : <WifiOff className="h-3 w-3" />}
            {isOnline ? "متصل" : "غير متصل"}
          </Badge>
        </div>

        {/* آخر مزامنة */}
        {lastSyncTime && (
          <div className="flex items-center justify-between">
            <span className="text-sm">آخر مزامنة:</span>
            <span className="text-xs text-muted-foreground">
              {lastSyncTime.toLocaleTimeString('ar-EG')}
            </span>
          </div>
        )}

        {/* رسالة خطأ */}
        {error && (
          <div className="flex items-start gap-2 p-2 bg-destructive/10 rounded-md">
            <AlertCircle className="h-4 w-4 text-destructive mt-0.5" />
            <div>
              <p className="text-sm font-medium text-destructive">خطأ:</p>
              <p className="text-xs text-destructive/80">{error}</p>
            </div>
          </div>
        )}

        {/* زر المزامنة */}
        {isReady && isOnline && (
          <Button 
            onClick={forceSync} 
            size="sm" 
            variant="outline" 
            className="w-full flex items-center gap-2"
          >
            <Sync className="h-3 w-3" />
            مزامنة الآن
          </Button>
        )}

        {/* معلومات إضافية */}
        <div className="text-xs text-muted-foreground space-y-1">
          {!isOnline && (
            <p>• يمكنك العمل بدون إنترنت، سيتم المزامنة عند الاتصال</p>
          )}
          {isOnline && isReady && (
            <p>• المزامنة التلقائية نشطة كل 30 ثانية</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default DatabaseStatus;
