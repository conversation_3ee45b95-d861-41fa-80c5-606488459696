import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON>, Printer, Download } from 'lucide-react';
import { useTheme } from '@/contexts/ThemeContext';

const ThemeToggle = () => {
  const { theme, toggleTheme } = useTheme();

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleTheme}
      className="relative h-9 w-9 rounded-full bg-gradient-to-r from-amber-50 to-blue-50 dark:from-slate-800 dark:to-slate-700 hover:from-amber-100 hover:to-blue-100 dark:hover:from-slate-700 dark:hover:to-slate-600 transition-all duration-300 border border-amber-200/50 dark:border-slate-600/50"
      title={theme === 'light' ? 'تفعيل الوضع الداكن' : 'تفعيل الوضع الفاتح'}
    >
      <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0 text-amber-600" />
      <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100 text-blue-600 dark:text-blue-400" />
      <span className="sr-only">تبديل الثيم</span>
    </Button>
  );
};

export default ThemeToggle;
