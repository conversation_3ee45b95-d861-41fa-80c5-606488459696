import React from "react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { 
  Calendar, 
  FileText, 
  Plus, 
  Search 
} from "lucide-react";

interface LayoutProps {
  children: React.ReactNode;
}

const Layout = ({ children }: LayoutProps) => {
  const location = useLocation();

  const menuItems = [
    { path: "/", label: "الرئيسية", icon: Calendar },
    { path: "/transactions", label: "دفتر المعاملات", icon: FileText },
    { path: "/customers", label: "التجار", icon: Search },
    { path: "/products", label: "المنتجات", icon: Plus },
    { path: "/reports", label: "التقارير", icon: FileText },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* الرأس العلوي */}
      <header className="bg-card shadow-card border-b border-border">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 rounded-lg gradient-primary flex items-center justify-center">
                <FileText className="w-6 h-6 text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-foreground">
                  برنامج المحاسبة اليومي
                </h1>
                <p className="text-sm text-muted-foreground">
                  إدارة العمليات التجارية والمبيعات
                </p>
              </div>
            </div>
            <Button variant="gradient" size="lg">
              <Plus className="w-4 h-4" />
              عملية جديدة
            </Button>
          </div>
        </div>
      </header>

      {/* شريط التنقل */}
      <nav className="bg-card border-b border-border">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-1">
            {menuItems.map((item) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.path;
              
              return (
                <Link key={item.path} to={item.path}>
                  <Button
                    variant={isActive ? "default" : "ghost"}
                    size="sm"
                    className="gap-2"
                  >
                    <Icon className="w-4 h-4" />
                    {item.label}
                  </Button>
                </Link>
              );
            })}
          </div>
        </div>
      </nav>

      {/* المحتوى الرئيسي */}
      <main className="container mx-auto px-4 py-8">
        {children}
      </main>
    </div>
  );
};

export default Layout;