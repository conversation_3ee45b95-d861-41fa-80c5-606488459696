import React from "react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import UserMenu from "@/components/UserMenu";
import ThemeToggle from "@/components/ThemeToggle";
import {
  Calendar,
  FileText,
  Plus,
  Search,
  Package,
  TrendingUp
} from "lucide-react";

interface LayoutProps {
  children: React.ReactNode;
}

const Layout = ({ children }: LayoutProps) => {
  const location = useLocation();

  const menuItems = [
    { path: "/", label: "الرئيسية", icon: Calendar },
    { path: "/transactions", label: "دفتر المعاملات", icon: FileText },
    { path: "/customers", label: "التجار", icon: Search },
    { path: "/products", label: "المنتجات", icon: Plus },
    { path: "/materials", label: "الخامات", icon: Package },
    { path: "/stock-transactions", label: "حركات المخزون", icon: TrendingUp },
    { path: "/reports", label: "التقارير", icon: FileText },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* الرأس العلوي */}
      <header className="bg-card shadow-card border-b border-border">
        <div className="container mx-auto px-3 sm:px-4 py-3 sm:py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 sm:gap-4">
              <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-lg gradient-primary flex items-center justify-center">
                <FileText className="w-4 h-4 sm:w-6 sm:h-6 text-primary-foreground" />
              </div>
              <div className="text-center sm:text-right">
                <h1 className="text-lg sm:text-2xl font-bold text-foreground">
                  برنامج المحاسبة اليومي
                </h1>
                <p className="text-xs sm:text-sm text-muted-foreground hidden sm:block">
                  إدارة العمليات التجارية والمبيعات
                </p>
              </div>
            </div>

            {/* أيقونات المستخدم والوضع الداكن */}
            <div className="flex items-center gap-2 sm:gap-3">
              <ThemeToggle />
              <UserMenu />
            </div>
          </div>
        </div>
      </header>

      {/* شريط التنقل */}
      <nav className="bg-card border-b border-border overflow-x-auto">
        <div className="container mx-auto px-2 sm:px-4">
          <div className="flex items-center gap-1 min-w-max sm:min-w-0 py-2">
            {menuItems.map((item) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.path;

              return (
                <Link key={item.path} to={item.path}>
                  <Button
                    variant={isActive ? "default" : "ghost"}
                    size="sm"
                    className="gap-1 sm:gap-2 text-xs sm:text-sm px-2 sm:px-3 whitespace-nowrap"
                  >
                    <Icon className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span className="hidden xs:inline">{item.label}</span>
                  </Button>
                </Link>
              );
            })}
          </div>
        </div>
      </nav>

      {/* المحتوى الرئيسي */}
      <main className="container mx-auto px-3 sm:px-4 py-4 sm:py-8">
        {children}
      </main>
    </div>
  );
};

export default Layout;