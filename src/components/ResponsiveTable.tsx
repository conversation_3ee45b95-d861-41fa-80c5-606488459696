import React from 'react';
import { Card } from '@/components/ui/card';

interface Column {
  key: string;
  label: string;
  className?: string;
  render?: (value: any, row: any) => React.ReactNode;
}

interface ResponsiveTableProps {
  columns: Column[];
  data: any[];
  emptyMessage?: string;
  className?: string;
}

const ResponsiveTable: React.FC<ResponsiveTableProps> = ({
  columns,
  data,
  emptyMessage = "لا توجد بيانات",
  className = ""
}) => {
  return (
    <div className={`overflow-x-auto ${className}`}>
      {/* عرض الجدول على الشاشات الكبيرة */}
      <div className="hidden md:block">
        <table className="w-full table-rtl">
          <thead>
            <tr className="border-b border-border">
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={`text-right py-3 px-4 font-medium text-muted-foreground text-sm ${column.className || ''}`}
                >
                  {column.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.length === 0 ? (
              <tr>
                <td colSpan={columns.length} className="py-8 text-center text-muted-foreground">
                  {emptyMessage}
                </td>
              </tr>
            ) : (
              data.map((row, index) => (
                <tr key={index} className="border-b border-border/50 hover:bg-muted/20 transition-smooth">
                  {columns.map((column) => (
                    <td
                      key={column.key}
                      className={`py-3 px-4 text-sm ${column.className || ''}`}
                    >
                      {column.render ? column.render(row[column.key], row) : row[column.key]}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* عرض البطاقات على الشاشات الصغيرة */}
      <div className="md:hidden space-y-3">
        {data.length === 0 ? (
          <Card className="p-6 text-center text-muted-foreground">
            {emptyMessage}
          </Card>
        ) : (
          data.map((row, index) => (
            <Card key={index} className="p-4 space-y-2">
              {columns.map((column) => (
                <div key={column.key} className="flex justify-between items-start">
                  <span className="text-sm font-medium text-muted-foreground min-w-0 flex-1">
                    {column.label}:
                  </span>
                  <span className={`text-sm text-right min-w-0 flex-1 ${column.className || ''}`}>
                    {column.render ? column.render(row[column.key], row) : row[column.key]}
                  </span>
                </div>
              ))}
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

export default ResponsiveTable;
