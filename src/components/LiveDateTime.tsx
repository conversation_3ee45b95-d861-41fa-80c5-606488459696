import React, { useState, useEffect } from 'react';

const LiveDateTime = () => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('ar-EG', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('ar-EG', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    });
  };

  return (
    <div className="text-center text-primary-foreground">
      <div className="text-xs sm:text-sm md:text-base opacity-90 mb-1 sm:mb-2">
        {formatDate(currentTime)}
      </div>
      <div className="text-sm sm:text-base md:text-lg font-semibold opacity-95">
        الساعة: {formatTime(currentTime)}
      </div>
    </div>
  );
};

export default LiveDateTime;
