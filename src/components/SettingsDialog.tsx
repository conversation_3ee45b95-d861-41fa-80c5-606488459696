import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  Settings, 
  Save, 
  RotateCcw, 
  Bell, 
  Database, 
  Palette, 
  Globe, 
  Shield,
  Download,
  Upload
} from 'lucide-react';
import { toast } from 'sonner';
import { useTheme } from '@/contexts/ThemeContext';

interface AppSettings {
  companyName: string;
  currency: string;
  language: string;
  notifications: boolean;
  autoSave: boolean;
  backupFrequency: string;
  dateFormat: string;
  numberFormat: string;
  taxRate: number;
  defaultPaymentMethod: string;
}

interface SettingsDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const SettingsDialog = ({ isOpen, onClose }: SettingsDialogProps) => {
  const { theme } = useTheme();
  const [settings, setSettings] = useState<AppSettings>({
    companyName: 'برنامج المحاسبة اليومي',
    currency: 'جنيه مصري',
    language: 'العربية',
    notifications: true,
    autoSave: true,
    backupFrequency: 'يومي',
    dateFormat: 'DD/MM/YYYY',
    numberFormat: '1,234.56',
    taxRate: 14,
    defaultPaymentMethod: 'نقدي'
  });

  // تحميل الإعدادات المحفوظة
  useEffect(() => {
    const savedSettings = localStorage.getItem('appSettings');
    if (savedSettings) {
      try {
        const parsedSettings = JSON.parse(savedSettings);
        setSettings(prev => ({ ...prev, ...parsedSettings }));
      } catch (error) {
        console.error('خطأ في تحميل الإعدادات:', error);
      }
    }
  }, []);

  const handleSaveSettings = () => {
    try {
      localStorage.setItem('appSettings', JSON.stringify(settings));
      toast.success('تم حفظ الإعدادات بنجاح');
      onClose();
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات:', error);
      toast.error('حدث خطأ في حفظ الإعدادات');
    }
  };

  const handleResetSettings = () => {
    setSettings({
      companyName: 'برنامج المحاسبة اليومي',
      currency: 'جنيه مصري',
      language: 'العربية',
      notifications: true,
      autoSave: true,
      backupFrequency: 'يومي',
      dateFormat: 'DD/MM/YYYY',
      numberFormat: '1,234.56',
      taxRate: 14,
      defaultPaymentMethod: 'نقدي'
    });
    toast.success('تم إعادة تعيين الإعدادات');
  };

  const handleExportSettings = () => {
    try {
      const dataStr = JSON.stringify(settings, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'app-settings.json';
      link.click();
      URL.revokeObjectURL(url);
      toast.success('تم تصدير الإعدادات بنجاح');
    } catch (error) {
      toast.error('حدث خطأ في تصدير الإعدادات');
    }
  };

  const handleImportSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importedSettings = JSON.parse(e.target?.result as string);
          setSettings(prev => ({ ...prev, ...importedSettings }));
          toast.success('تم استيراد الإعدادات بنجاح');
        } catch (error) {
          toast.error('ملف الإعدادات غير صالح');
        }
      };
      reader.readAsText(file);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            إعدادات البرنامج
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* إعدادات الشركة */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                إعدادات الشركة
              </CardTitle>
              <CardDescription>
                معلومات أساسية عن الشركة والعملة
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="companyName">اسم الشركة</Label>
                  <Input
                    id="companyName"
                    value={settings.companyName}
                    onChange={(e) => setSettings(prev => ({ ...prev, companyName: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="currency">العملة</Label>
                  <Select 
                    value={settings.currency} 
                    onValueChange={(value) => setSettings(prev => ({ ...prev, currency: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="جنيه مصري">جنيه مصري (EGP)</SelectItem>
                      <SelectItem value="ريال سعودي">ريال سعودي (SAR)</SelectItem>
                      <SelectItem value="درهم إماراتي">درهم إماراتي (AED)</SelectItem>
                      <SelectItem value="دولار أمريكي">دولار أمريكي (USD)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="taxRate">معدل الضريبة (%)</Label>
                  <Input
                    id="taxRate"
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    value={settings.taxRate}
                    onChange={(e) => setSettings(prev => ({ ...prev, taxRate: parseFloat(e.target.value) || 0 }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="defaultPaymentMethod">طريقة الدفع الافتراضية</Label>
                  <Select 
                    value={settings.defaultPaymentMethod} 
                    onValueChange={(value) => setSettings(prev => ({ ...prev, defaultPaymentMethod: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="نقدي">نقدي</SelectItem>
                      <SelectItem value="بطاقة ائتمان">بطاقة ائتمان</SelectItem>
                      <SelectItem value="تحويل بنكي">تحويل بنكي</SelectItem>
                      <SelectItem value="شيك">شيك</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* إعدادات التطبيق */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-4 w-4" />
                إعدادات التطبيق
              </CardTitle>
              <CardDescription>
                تخصيص واجهة المستخدم والتفضيلات
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="dateFormat">تنسيق التاريخ</Label>
                  <Select 
                    value={settings.dateFormat} 
                    onValueChange={(value) => setSettings(prev => ({ ...prev, dateFormat: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                      <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                      <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="numberFormat">تنسيق الأرقام</Label>
                  <Select 
                    value={settings.numberFormat} 
                    onValueChange={(value) => setSettings(prev => ({ ...prev, numberFormat: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1,234.56">1,234.56</SelectItem>
                      <SelectItem value="1.234,56">1.234,56</SelectItem>
                      <SelectItem value="1 234.56">1 234.56</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="flex items-center gap-2">
                      <Bell className="h-4 w-4" />
                      التنبيهات
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      تفعيل التنبيهات والإشعارات
                    </p>
                  </div>
                  <Switch
                    checked={settings.notifications}
                    onCheckedChange={(checked) => setSettings(prev => ({ ...prev, notifications: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="flex items-center gap-2">
                      <Database className="h-4 w-4" />
                      الحفظ التلقائي
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      حفظ البيانات تلقائياً أثناء العمل
                    </p>
                  </div>
                  <Switch
                    checked={settings.autoSave}
                    onCheckedChange={(checked) => setSettings(prev => ({ ...prev, autoSave: checked }))}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Separator />

          {/* أزرار التحكم */}
          <div className="flex flex-wrap gap-3">
            <Button onClick={handleSaveSettings} className="flex-1 min-w-[120px]">
              <Save className="w-4 h-4 ml-2" />
              حفظ الإعدادات
            </Button>
            
            <Button variant="outline" onClick={handleResetSettings} className="flex-1 min-w-[120px]">
              <RotateCcw className="w-4 h-4 ml-2" />
              إعادة تعيين
            </Button>
            
            <Button variant="outline" onClick={handleExportSettings} className="flex-1 min-w-[120px]">
              <Download className="w-4 h-4 ml-2" />
              تصدير الإعدادات
            </Button>
            
            <div className="flex-1 min-w-[120px]">
              <input
                type="file"
                accept=".json"
                onChange={handleImportSettings}
                className="hidden"
                id="import-settings"
              />
              <Button 
                variant="outline" 
                onClick={() => document.getElementById('import-settings')?.click()}
                className="w-full"
              >
                <Upload className="w-4 h-4 ml-2" />
                استيراد الإعدادات
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SettingsDialog;
