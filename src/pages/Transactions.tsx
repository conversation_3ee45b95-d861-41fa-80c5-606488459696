import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FileText, Plus, Search, Calendar, ArrowRight } from "lucide-react";
import { Link } from "react-router-dom";
import { useData } from "@/contexts/DataContext";
import ResponsiveTable from "@/components/ResponsiveTable";

const Transactions = () => {
  const { transactions } = useData();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCustomer, setSelectedCustomer] = useState("");
  const [selectedDate, setSelectedDate] = useState("");

  const customers = [...new Set(transactions.map(t => t.customerName))];

  // تصفية المعاملات
  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = transaction.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transaction.product.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCustomer = !selectedCustomer || selectedCustomer === "all" || transaction.customerName === selectedCustomer;
    const matchesDate = !selectedDate || transaction.date === selectedDate;
    
    return matchesSearch && matchesCustomer && matchesDate;
  });

  // حساب الإحصائيات
  const stats = {
    totalTransactions: filteredTransactions.length,
    totalSales: filteredTransactions.reduce((sum, t) => sum + t.totalAmount, 0),
    totalPaid: filteredTransactions.reduce((sum, t) => sum + t.paidAmount, 0),
    totalRemaining: filteredTransactions.reduce((sum, t) => sum + t.remainingAmount, 0),
  };

  // تعريف أعمدة الجدول
  const tableColumns = [
    { key: 'index', label: '#', className: 'font-medium numbers-ltr' },
    { key: 'customerName', label: 'التاجر', className: 'font-medium' },
    { key: 'product', label: 'المنتج' },
    {
      key: 'quantity',
      label: 'الكمية',
      className: 'numbers-ltr',
      render: (value: string) => `${value} وحدة`
    },
    {
      key: 'pricePerUnit',
      label: 'السعر/وحدة',
      className: 'numbers-ltr',
      render: (value: number) => `${value} ج.م`
    },
    {
      key: 'totalAmount',
      label: 'المبلغ الكلي',
      className: 'numbers-ltr font-medium',
      render: (value: number) => `${value.toLocaleString()} ج.م`
    },
    {
      key: 'paidAmount',
      label: 'المدفوع',
      className: 'numbers-ltr text-success',
      render: (value: number) => `${value.toLocaleString()} ج.م`
    },
    {
      key: 'remainingAmount',
      label: 'المتبقي',
      className: 'numbers-ltr',
      render: (value: number, row: any) => (
        <span className={value > 0 ? "text-destructive font-medium" : "text-success"}>
          {value.toLocaleString()} ج.م
        </span>
      )
    },
    {
      key: 'date',
      label: 'التاريخ',
      className: 'text-muted-foreground',
      render: (value: string) => new Date(value).toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    },
    {
      key: 'time',
      label: 'الوقت',
      className: 'text-muted-foreground numbers-ltr'
    }
  ];

  // إعداد البيانات للجدول
  const tableData = filteredTransactions.map((transaction, index) => ({
    ...transaction,
    index: index + 1
  }));

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* رأس الصفحة */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-4">
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4 w-full sm:w-auto">
          <Link to="/">
            <Button variant="ghost" size="sm" className="text-xs sm:text-sm">
              <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4" />
              العودة للرئيسية
            </Button>
          </Link>
          <div>
            <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-foreground">دفتر المعاملات</h1>
            <p className="text-sm sm:text-base text-muted-foreground">جميع العمليات التجارية والمبيعات</p>
          </div>
        </div>
        <Link to="/add-transaction" className="w-full sm:w-auto">
          <Button variant="gradient" size="lg" className="w-full sm:w-auto text-sm sm:text-base">
            <Plus className="w-4 h-4 sm:w-5 sm:h-5" />
            إضافة عملية جديدة
          </Button>
        </Link>
      </div>

      {/* أدوات التصفية */}
      <Card className="p-4 sm:p-6 shadow-card">
        <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 text-foreground">تصفية المعاملات</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">البحث</label>
            <Input
              placeholder="ابحث باسم التاجر أو المنتج..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">التاجر</label>
            <Select value={selectedCustomer} onValueChange={setSelectedCustomer}>
              <SelectTrigger>
                <SelectValue placeholder="جميع التجار" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع التجار</SelectItem>
                {customers.map((customer) => (
                  <SelectItem key={customer} value={customer}>
                    {customer}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">التاريخ</label>
            <Input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
            />
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 pt-6 border-t border-border">
          <div className="text-center">
            <p className="text-2xl font-bold text-primary numbers-ltr">{stats.totalTransactions}</p>
            <p className="text-sm text-muted-foreground">إجمالي المعاملات</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-success numbers-ltr">{stats.totalSales.toLocaleString()}</p>
            <p className="text-sm text-muted-foreground">إجمالي المبيعات</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-primary numbers-ltr">{stats.totalPaid.toLocaleString()}</p>
            <p className="text-sm text-muted-foreground">إجمالي المدفوع</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-destructive numbers-ltr">{stats.totalRemaining.toLocaleString()}</p>
            <p className="text-sm text-muted-foreground">إجمالي المتبقي</p>
          </div>
        </div>
      </Card>

      {/* جدول المعاملات */}
      <Card className="shadow-card">
        <div className="p-6 border-b border-border">
          <h3 className="text-lg font-semibold text-foreground">
            قائمة المعاملات ({filteredTransactions.length} معاملة)
          </h3>
        </div>
        
        <ResponsiveTable
          columns={tableColumns}
          data={tableData}
          emptyMessage={
            transactions.length === 0 ? (
              <div className="space-y-2">
                <p>لا توجد معاملات حتى الآن</p>
                <Link to="/add-transaction" className="text-primary hover:underline block">
                  ابدأ بإضافة أول معاملة
                </Link>
              </div>
            ) : (
              "لا توجد معاملات مطابقة للبحث"
            )
          }
        />
      </Card>

      {/* أزرار إضافية */}
      <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
        <Button variant="outline" size="lg" className="w-full sm:w-auto text-sm sm:text-base">
          <FileText className="w-4 h-4 sm:w-5 sm:h-5" />
          تصدير PDF
        </Button>
        <Button variant="outline" size="lg" className="w-full sm:w-auto text-sm sm:text-base">
          <Calendar className="w-4 h-4 sm:w-5 sm:h-5" />
          تصدير Excel
        </Button>
      </div>
    </div>
  );
};

export default Transactions;