import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, Di<PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FileText, Plus, Search, Calendar, ArrowRight, Edit, Trash2, Save, X } from "lucide-react";
import { Link } from "react-router-dom";
import { useData } from "@/contexts/DataContext";
import ResponsiveTable from "@/components/ResponsiveTable";
import { toast as sonnerToast } from "sonner";

const Transactions = () => {
  const { transactions, customers, products, updateTransaction, deleteTransaction } = useData();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCustomer, setSelectedCustomer] = useState("");
  const [selectedDate, setSelectedDate] = useState("");
  const [editingTransaction, setEditingTransaction] = useState<any>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  const customers = [...new Set(transactions.map(t => t.customerName))];

  // تصفية المعاملات
  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = transaction.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transaction.product.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCustomer = !selectedCustomer || selectedCustomer === "all" || transaction.customerName === selectedCustomer;
    const matchesDate = !selectedDate || transaction.date === selectedDate;
    
    return matchesSearch && matchesCustomer && matchesDate;
  });

  // حساب الإحصائيات
  const stats = {
    totalTransactions: filteredTransactions.length,
    totalSales: filteredTransactions.reduce((sum, t) => sum + t.totalAmount, 0),
    totalPaid: filteredTransactions.reduce((sum, t) => sum + t.paidAmount, 0),
    totalRemaining: filteredTransactions.reduce((sum, t) => sum + t.remainingAmount, 0),
  };

  // تعريف أعمدة الجدول
  const tableColumns = [
    { key: 'index', label: '#', className: 'font-medium numbers-ltr' },
    { key: 'customerName', label: 'التاجر', className: 'font-medium' },
    { key: 'product', label: 'المنتج' },
    {
      key: 'quantity',
      label: 'الكمية',
      className: 'numbers-ltr',
      render: (value: string) => `${value} وحدة`
    },
    {
      key: 'pricePerUnit',
      label: 'السعر/وحدة',
      className: 'numbers-ltr',
      render: (value: number) => `${value} ج.م`
    },
    {
      key: 'totalAmount',
      label: 'المبلغ الكلي',
      className: 'numbers-ltr font-medium',
      render: (value: number) => `${value.toLocaleString()} ج.م`
    },
    {
      key: 'paidAmount',
      label: 'المدفوع',
      className: 'numbers-ltr text-success',
      render: (value: number) => `${value.toLocaleString()} ج.م`
    },
    {
      key: 'remainingAmount',
      label: 'المتبقي',
      className: 'numbers-ltr',
      render: (value: number, row: any) => (
        <span className={value > 0 ? "text-destructive font-medium" : "text-success"}>
          {value.toLocaleString()} ج.م
        </span>
      )
    },
    {
      key: 'date',
      label: 'التاريخ',
      className: 'text-muted-foreground',
      render: (value: string) => new Date(value).toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    },
    {
      key: 'time',
      label: 'الوقت',
      className: 'text-muted-foreground numbers-ltr'
    },
    {
      key: 'actions',
      label: 'الإجراءات',
      className: 'text-center',
      render: (value: any, row: any) => (
        <div className="flex gap-2 justify-center">
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleEditTransaction(row)}
            className="h-8 w-8 p-0"
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleDeleteTransaction(row.id)}
            className="h-8 w-8 p-0 text-destructive hover:text-destructive"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  // وظائف التعديل والحذف
  const handleEditTransaction = (transaction: any) => {
    setEditingTransaction({
      ...transaction,
      totalAmount: parseFloat(transaction.totalAmount),
      paidAmount: parseFloat(transaction.paidAmount),
      remainingAmount: parseFloat(transaction.remainingAmount),
      pricePerUnit: parseFloat(transaction.pricePerUnit),
      quantity: parseFloat(transaction.quantity)
    });
    setIsEditDialogOpen(true);
  };

  const handleSaveEdit = async () => {
    if (!editingTransaction) return;

    try {
      const updatedTransaction = {
        ...editingTransaction,
        totalAmount: editingTransaction.quantity * editingTransaction.pricePerUnit,
        remainingAmount: (editingTransaction.quantity * editingTransaction.pricePerUnit) - editingTransaction.paidAmount
      };

      await updateTransaction(editingTransaction.id, updatedTransaction);
      setIsEditDialogOpen(false);
      setEditingTransaction(null);
      sonnerToast.success("تم تحديث المعاملة بنجاح");
    } catch (error) {
      sonnerToast.error("حدث خطأ في تحديث المعاملة");
    }
  };

  const handleDeleteTransaction = async (id: string) => {
    if (window.confirm("هل أنت متأكد من حذف هذه المعاملة؟")) {
      try {
        await deleteTransaction(id);
        sonnerToast.success("تم حذف المعاملة بنجاح");
      } catch (error) {
        sonnerToast.error("حدث خطأ في حذف المعاملة");
      }
    }
  };

  // إعداد البيانات للجدول
  const tableData = filteredTransactions.map((transaction, index) => ({
    ...transaction,
    index: index + 1
  }));

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* رأس الصفحة */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-4">
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4 w-full sm:w-auto">
          <Link to="/">
            <Button variant="ghost" size="sm" className="text-xs sm:text-sm">
              <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4" />
              العودة للرئيسية
            </Button>
          </Link>
          <div>
            <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-foreground">دفتر المعاملات</h1>
            <p className="text-sm sm:text-base text-muted-foreground">جميع العمليات التجارية والمبيعات</p>
          </div>
        </div>
        <Link to="/add-transaction" className="w-full sm:w-auto">
          <Button variant="gradient" size="lg" className="w-full sm:w-auto text-sm sm:text-base">
            <Plus className="w-4 h-4 sm:w-5 sm:h-5" />
            إضافة عملية جديدة
          </Button>
        </Link>
      </div>

      {/* أدوات التصفية */}
      <Card className="p-4 sm:p-6 shadow-card">
        <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 text-foreground">تصفية المعاملات</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">البحث</label>
            <Input
              placeholder="ابحث باسم التاجر أو المنتج..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">التاجر</label>
            <Select value={selectedCustomer} onValueChange={setSelectedCustomer}>
              <SelectTrigger>
                <SelectValue placeholder="جميع التجار" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع التجار</SelectItem>
                {customers.map((customer) => (
                  <SelectItem key={customer} value={customer}>
                    {customer}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">التاريخ</label>
            <Input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
            />
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 pt-6 border-t border-border">
          <div className="text-center">
            <p className="text-2xl font-bold text-primary numbers-ltr">{stats.totalTransactions}</p>
            <p className="text-sm text-muted-foreground">إجمالي المعاملات</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-success numbers-ltr">{stats.totalSales.toLocaleString()}</p>
            <p className="text-sm text-muted-foreground">إجمالي المبيعات</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-primary numbers-ltr">{stats.totalPaid.toLocaleString()}</p>
            <p className="text-sm text-muted-foreground">إجمالي المدفوع</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-destructive numbers-ltr">{stats.totalRemaining.toLocaleString()}</p>
            <p className="text-sm text-muted-foreground">إجمالي المتبقي</p>
          </div>
        </div>
      </Card>

      {/* جدول المعاملات */}
      <Card className="shadow-card">
        <div className="p-6 border-b border-border">
          <h3 className="text-lg font-semibold text-foreground">
            قائمة المعاملات ({filteredTransactions.length} معاملة)
          </h3>
        </div>
        
        <ResponsiveTable
          columns={tableColumns}
          data={tableData}
          emptyMessage={
            transactions.length === 0 ? (
              <div className="space-y-2">
                <p>لا توجد معاملات حتى الآن</p>
                <Link to="/add-transaction" className="text-primary hover:underline block">
                  ابدأ بإضافة أول معاملة
                </Link>
              </div>
            ) : (
              "لا توجد معاملات مطابقة للبحث"
            )
          }
        />
      </Card>

      {/* أزرار إضافية */}
      <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
        <Button variant="outline" size="lg" className="w-full sm:w-auto text-sm sm:text-base">
          <FileText className="w-4 h-4 sm:w-5 sm:h-5" />
          تصدير PDF
        </Button>
        <Button variant="outline" size="lg" className="w-full sm:w-auto text-sm sm:text-base">
          <Calendar className="w-4 h-4 sm:w-5 sm:h-5" />
          تصدير Excel
        </Button>
      </div>

      {/* نافذة تعديل المعاملة */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>تعديل المعاملة</DialogTitle>
          </DialogHeader>

          {editingTransaction && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>اسم التاجر</Label>
                  <Select
                    value={editingTransaction.customerName}
                    onValueChange={(value) => setEditingTransaction(prev => ({ ...prev, customerName: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {customers.map((customer) => (
                        <SelectItem key={customer.id} value={customer.name}>
                          {customer.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>المنتج</Label>
                  <Select
                    value={editingTransaction.product}
                    onValueChange={(value) => setEditingTransaction(prev => ({ ...prev, product: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {products.map((product) => (
                        <SelectItem key={product.id} value={product.name}>
                          {product.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>الكمية</Label>
                  <Input
                    type="number"
                    step="0.1"
                    value={editingTransaction.quantity}
                    onChange={(e) => setEditingTransaction(prev => ({ ...prev, quantity: parseFloat(e.target.value) || 0 }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label>السعر للوحدة</Label>
                  <Input
                    type="number"
                    step="0.01"
                    value={editingTransaction.pricePerUnit}
                    onChange={(e) => setEditingTransaction(prev => ({ ...prev, pricePerUnit: parseFloat(e.target.value) || 0 }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label>المبلغ المدفوع</Label>
                  <Input
                    type="number"
                    step="0.01"
                    value={editingTransaction.paidAmount}
                    onChange={(e) => setEditingTransaction(prev => ({ ...prev, paidAmount: parseFloat(e.target.value) || 0 }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label>التاريخ</Label>
                  <Input
                    type="date"
                    value={editingTransaction.date}
                    onChange={(e) => setEditingTransaction(prev => ({ ...prev, date: e.target.value }))}
                  />
                </div>
              </div>

              <div className="bg-muted p-4 rounded-lg">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">المبلغ الكلي: </span>
                    <span className="font-medium numbers-ltr">
                      {(editingTransaction.quantity * editingTransaction.pricePerUnit).toLocaleString()} ج.م
                    </span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">المتبقي: </span>
                    <span className="font-medium numbers-ltr">
                      {((editingTransaction.quantity * editingTransaction.pricePerUnit) - editingTransaction.paidAmount).toLocaleString()} ج.م
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex gap-4 pt-4">
                <Button onClick={handleSaveEdit} className="flex-1">
                  <Save className="w-4 h-4 ml-2" />
                  حفظ التغييرات
                </Button>
                <Button variant="outline" onClick={() => setIsEditDialogOpen(false)} className="flex-1">
                  <X className="w-4 h-4 ml-2" />
                  إلغاء
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Transactions;