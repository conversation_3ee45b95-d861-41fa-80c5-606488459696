import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar, Plus, ArrowRight, Save } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Link, useNavigate } from "react-router-dom";
import { useData } from "@/contexts/DataContext";

const AddTransaction = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { customers, products, addTransaction } = useData();

  const [formData, setFormData] = useState({
    customerName: "",
    product: "",
    quantity: "",
    pricePerUnit: "",
    totalAmount: "",
    paidAmount: "",
    remainingAmount: "",
    date: new Date().toISOString().split('T')[0],
  });

  // الحصول على أسماء العملاء والمنتجات من البيانات المحفوظة
  const existingCustomers = customers.map(customer => customer.name);
  const productNames = products.map(product => product.name);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => {
      const updated = { ...prev, [field]: value };
      
      // حساب المبلغ الكلي تلقائياً
      if (field === "quantity" || field === "pricePerUnit") {
        const quantity = parseFloat(field === "quantity" ? value : updated.quantity) || 0;
        const price = parseFloat(field === "pricePerUnit" ? value : updated.pricePerUnit) || 0;
        updated.totalAmount = (quantity * price).toString();
      }
      
      // حساب المبلغ المتبقي تلقائياً
      if (field === "totalAmount" || field === "paidAmount") {
        const total = parseFloat(field === "totalAmount" ? value : updated.totalAmount) || 0;
        const paid = parseFloat(field === "paidAmount" ? value : updated.paidAmount) || 0;
        updated.remainingAmount = (total - paid).toString();
      }
      
      return updated;
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // التحقق من البيانات
    if (!formData.customerName || !formData.product || !formData.quantity || !formData.totalAmount) {
      toast({
        title: "خطأ في البيانات",
        description: "يرجى ملء جميع الحقول المطلوبة",
        variant: "destructive",
      });
      return;
    }

    // إنشاء كائن المعاملة
    const transaction = {
      customerName: formData.customerName,
      product: formData.product,
      quantity: formData.quantity,
      pricePerUnit: parseFloat(formData.pricePerUnit) || 0,
      totalAmount: parseFloat(formData.totalAmount),
      paidAmount: parseFloat(formData.paidAmount) || 0,
      remainingAmount: parseFloat(formData.remainingAmount) || 0,
      date: formData.date,
      time: new Date().toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' }),
    };

    // حفظ المعاملة
    addTransaction(transaction);

    toast({
      title: "تم إضافة العملية بنجاح",
      description: `تم تسجيل عملية بيع ${formData.product} للعميل ${formData.customerName}`,
    });

    // إعادة تعيين النموذج
    setFormData({
      customerName: "",
      product: "",
      quantity: "",
      pricePerUnit: "",
      totalAmount: "",
      paidAmount: "",
      remainingAmount: "",
      date: new Date().toISOString().split('T')[0],
    });

    // العودة إلى الصفحة الرئيسية
    setTimeout(() => {
      navigate('/');
    }, 1500);
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* رأس الصفحة */}
      <div className="flex items-center gap-4">
        <Link to="/">
          <Button variant="ghost" size="sm">
            <ArrowRight className="w-4 h-4" />
            العودة للرئيسية
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-foreground">إضافة عملية بيع جديدة</h1>
          <p className="text-muted-foreground">تسجيل عملية بيع جديدة مع تفاصيل الدفع</p>
        </div>
      </div>

      <Card className="p-8 shadow-card">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* معلومات العميل */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-primary"></div>
                معلومات العميل
              </h3>
              
              <div className="space-y-2">
                <Label htmlFor="customer">اسم التاجر / العميل *</Label>
                <Select 
                  value={formData.customerName} 
                  onValueChange={(value) => handleInputChange("customerName", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر عميل موجود أو اكتب اسم جديد" />
                  </SelectTrigger>
                  <SelectContent>
                    {existingCustomers.map((customer) => (
                      <SelectItem key={customer} value={customer}>
                        {customer}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Input
                  placeholder="أو اكتب اسم عميل جديد"
                  value={formData.customerName}
                  onChange={(e) => handleInputChange("customerName", e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="date">تاريخ العملية</Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => handleInputChange("date", e.target.value)}
                />
              </div>
            </div>

            {/* معلومات المنتج */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-success"></div>
                معلومات المنتج
              </h3>
              
              <div className="space-y-2">
                <Label htmlFor="product">نوع المنتج *</Label>
                <Select
                  value={formData.product}
                  onValueChange={(value) => handleInputChange("product", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر منتج موجود أو اكتب اسم جديد" />
                  </SelectTrigger>
                  <SelectContent>
                    {productNames.map((product) => (
                      <SelectItem key={product} value={product}>
                        {product}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Input
                  placeholder="أو اكتب اسم منتج جديد"
                  value={formData.product}
                  onChange={(e) => handleInputChange("product", e.target.value)}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="quantity">الكمية *</Label>
                  <Input
                    id="quantity"
                    type="number"
                    placeholder="50"
                    value={formData.quantity}
                    onChange={(e) => handleInputChange("quantity", e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground">بالكيلوغرام</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="pricePerUnit">السعر للكيلو</Label>
                  <Input
                    id="pricePerUnit"
                    type="number"
                    step="0.01"
                    placeholder="10.50"
                    value={formData.pricePerUnit}
                    onChange={(e) => handleInputChange("pricePerUnit", e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground">جنيه مصري</p>
                </div>
              </div>
            </div>
          </div>

          {/* معلومات مالية */}
          <div className="border-t border-border pt-6">
            <h3 className="text-lg font-semibold text-foreground flex items-center gap-2 mb-4">
              <div className="w-2 h-2 rounded-full bg-warning"></div>
              المعلومات المالية
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="totalAmount">المبلغ الكلي *</Label>
                <Input
                  id="totalAmount"
                  type="number"
                  step="0.01"
                  placeholder="525.00"
                  value={formData.totalAmount}
                  onChange={(e) => handleInputChange("totalAmount", e.target.value)}
                />
                <p className="text-xs text-muted-foreground">سيتم حسابه تلقائياً</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="paidAmount">المبلغ المدفوع</Label>
                <Input
                  id="paidAmount"
                  type="number"
                  step="0.01"
                  placeholder="400.00"
                  value={formData.paidAmount}
                  onChange={(e) => handleInputChange("paidAmount", e.target.value)}
                />
                <p className="text-xs text-muted-foreground">المبلغ المدفوع فعلياً</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="remainingAmount">المبلغ المتبقي</Label>
                <Input
                  id="remainingAmount"
                  type="number"
                  step="0.01"
                  value={formData.remainingAmount}
                  readOnly
                  className="bg-muted"
                />
                <p className="text-xs text-muted-foreground">سيتم حسابه تلقائياً</p>
              </div>
            </div>

            {/* ملخص المعاملة */}
            {formData.totalAmount && (
              <div className="mt-6 p-4 bg-accent rounded-lg">
                <h4 className="font-medium text-accent-foreground mb-2">ملخص المعاملة:</h4>
                <div className="space-y-1 text-sm">
                  <p>المنتج: <span className="font-medium">{formData.product}</span></p>
                  <p>الكمية: <span className="font-medium numbers-ltr">{formData.quantity} كيلو</span></p>
                  <p>المبلغ الكلي: <span className="font-medium numbers-ltr text-primary">{parseFloat(formData.totalAmount || "0").toLocaleString()} ج.م</span></p>
                  <p>المبلغ المدفوع: <span className="font-medium numbers-ltr text-success">{parseFloat(formData.paidAmount || "0").toLocaleString()} ج.م</span></p>
                  <p>المبلغ المتبقي: <span className={`font-medium numbers-ltr ${parseFloat(formData.remainingAmount || "0") > 0 ? "text-destructive" : "text-success"}`}>{parseFloat(formData.remainingAmount || "0").toLocaleString()} ج.م</span></p>
                </div>
              </div>
            )}
          </div>

          {/* أزرار الإجراءات */}
          <div className="flex gap-4 pt-6 border-t border-border">
            <Button type="submit" variant="gradient" size="lg" className="flex-1">
              <Plus className="w-5 h-5" />
              حفظ العملية
            </Button>
            <Button type="button" variant="outline" size="lg" onClick={() => window.history.back()}>
              إلغاء
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
};

export default AddTransaction;