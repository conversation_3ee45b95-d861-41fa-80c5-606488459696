import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Plus, ArrowRight, Search, Package } from "lucide-react";
import { Link } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { useData } from "@/contexts/DataContext";
import { Textarea } from "@/components/ui/textarea";

const Products = () => {
  const { toast } = useToast();
  const { products, addProduct } = useData();
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddingProduct, setIsAddingProduct] = useState(false);
  const [newProduct, setNewProduct] = useState({
    name: "",
    defaultPrice: "",
    unit: "كيلو",
    description: "",
  });

  // تصفية المنتجات
  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // إضافة منتج جديد
  const handleAddProduct = (e: React.FormEvent) => {
    e.preventDefault();

    if (!newProduct.name || !newProduct.defaultPrice) {
      toast({
        title: "خطأ في البيانات",
        description: "يرجى ملء اسم المنتج والسعر",
        variant: "destructive",
      });
      return;
    }

    const productData = {
      name: newProduct.name,
      defaultPrice: parseFloat(newProduct.defaultPrice),
      unit: newProduct.unit,
      description: newProduct.description,
    };

    addProduct(productData);
    setNewProduct({ name: "", defaultPrice: "", unit: "كيلو", description: "" });
    setIsAddingProduct(false);

    toast({
      title: "تم إضافة المنتج بنجاح",
      description: `تم إضافة ${productData.name} إلى قائمة المنتجات`,
    });
  };

  // حساب الإحصائيات
  const stats = {
    totalProducts: filteredProducts.length,
    totalRevenue: filteredProducts.reduce((sum, p) => sum + p.revenue, 0),
    totalQuantitySold: filteredProducts.reduce((sum, p) => sum + p.totalSold, 0),
    averagePrice: filteredProducts.length > 0 
      ? filteredProducts.reduce((sum, p) => sum + p.defaultPrice, 0) / filteredProducts.length 
      : 0,
  };

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link to="/">
            <Button variant="ghost" size="sm">
              <ArrowRight className="w-4 h-4" />
              العودة للرئيسية
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-foreground">إدارة المنتجات</h1>
            <p className="text-muted-foreground">قائمة المنتجات والأسعار</p>
          </div>
        </div>
        <Button 
          variant="gradient" 
          size="lg"
          onClick={() => setIsAddingProduct(true)}
        >
          <Plus className="w-5 h-5" />
          إضافة منتج جديد
        </Button>
      </div>

      {/* أدوات البحث والإحصائيات */}
      <Card className="p-6 shadow-card">
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between mb-6">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="ابحث عن المنتج..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pr-10"
            />
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-primary/5 rounded-lg">
            <p className="text-2xl font-bold text-primary numbers-ltr">{stats.totalProducts}</p>
            <p className="text-sm text-muted-foreground">إجمالي المنتجات</p>
          </div>
          <div className="text-center p-4 bg-success/5 rounded-lg">
            <p className="text-2xl font-bold text-success numbers-ltr">{stats.totalRevenue.toLocaleString()}</p>
            <p className="text-sm text-muted-foreground">إجمالي الإيرادات</p>
          </div>
          <div className="text-center p-4 bg-secondary-accent/5 rounded-lg">
            <p className="text-2xl font-bold text-secondary-accent numbers-ltr">{stats.totalQuantitySold}</p>
            <p className="text-sm text-muted-foreground">الكمية المباعة</p>
          </div>
          <div className="text-center p-4 bg-warning/5 rounded-lg">
            <p className="text-2xl font-bold text-warning numbers-ltr">{stats.averagePrice.toFixed(0)}</p>
            <p className="text-sm text-muted-foreground">متوسط السعر</p>
          </div>
        </div>
      </Card>

      {/* نموذج إضافة منتج */}
      {isAddingProduct && (
        <Card className="p-6 shadow-card">
          <h3 className="text-lg font-semibold mb-4 text-foreground">إضافة منتج جديد</h3>
          <form onSubmit={handleAddProduct} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="productName">اسم المنتج *</Label>
                <Input
                  id="productName"
                  placeholder="مثال: باذنجان"
                  value={newProduct.name}
                  onChange={(e) => setNewProduct(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="defaultPrice">السعر الافتراضي *</Label>
                <Input
                  id="defaultPrice"
                  type="number"
                  step="0.01"
                  placeholder="25.50"
                  value={newProduct.defaultPrice}
                  onChange={(e) => setNewProduct(prev => ({ ...prev, defaultPrice: e.target.value }))}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">وصف المنتج</Label>
              <Input
                id="description"
                placeholder="وصف مختصر للمنتج..."
                value={newProduct.description}
                onChange={(e) => setNewProduct(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>

            <div className="flex gap-4">
              <Button type="submit" variant="gradient">
                <Plus className="w-4 h-4" />
                حفظ المنتج
              </Button>
              <Button 
                type="button" 
                variant="outline"
                onClick={() => setIsAddingProduct(false)}
              >
                إلغاء
              </Button>
            </div>
          </form>
        </Card>
      )}

      {/* قائمة المنتجات */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredProducts.length === 0 ? (
          <Card className="col-span-full p-8 text-center shadow-card">
            <div className="space-y-4">
              {products.length === 0 ? (
                <>
                  <p className="text-muted-foreground text-lg">لا توجد منتجات مضافة حتى الآن</p>
                  <p className="text-sm text-muted-foreground">
                    ابدأ بإضافة المنتجات التي تتاجر بها
                  </p>
                  <Button
                    variant="gradient"
                    className="mt-4"
                    onClick={() => setIsAddingProduct(true)}
                  >
                    <Plus className="w-4 h-4 ml-2" />
                    إضافة أول منتج
                  </Button>
                </>
              ) : (
                <p className="text-muted-foreground">لا توجد منتجات مطابقة للبحث</p>
              )}
            </div>
          </Card>
        ) : (
          filteredProducts.map((product) => (
            <Card key={product.id} className="p-6 shadow-card transition-smooth hover:shadow-elevated">
              <div className="space-y-4">
                {/* معلومات المنتج */}
                <div>
                  <h3 className="text-lg font-semibold text-foreground mb-1">
                    {product.name}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {product.description || "لا يوجد وصف"}
                  </p>
                </div>

                {/* السعر */}
                <div className="flex items-center justify-between p-3 bg-primary/5 rounded-lg">
                  <span className="text-sm text-muted-foreground">السعر الافتراضي:</span>
                  <span className="text-xl font-bold text-primary numbers-ltr">
                    {product.defaultPrice} ر.س / {product.unit}
                  </span>
                </div>

                {/* الإحصائيات */}
                <div className="grid grid-cols-2 gap-3">
                  <div className="text-center p-3 bg-muted/50 rounded-lg">
                    <p className="text-sm text-muted-foreground">الكمية المباعة</p>
                    <p className="text-lg font-bold text-success numbers-ltr">
                      {product.totalSold} {product.unit}
                    </p>
                  </div>
                  <div className="text-center p-3 bg-muted/50 rounded-lg">
                    <p className="text-sm text-muted-foreground">إجمالي الإيرادات</p>
                    <p className="text-lg font-bold text-secondary-accent numbers-ltr">
                      {product.revenue.toLocaleString()} ر.س
                    </p>
                  </div>
                </div>

                {/* آخر بيع */}
                {product.lastSale && (
                  <div className="text-center p-2 bg-accent rounded-lg">
                    <p className="text-xs text-muted-foreground">
                      آخر بيع: {new Date(product.lastSale).toLocaleDateString('ar-SA')}
                    </p>
                  </div>
                )}

                {/* لا يوجد مبيعات */}
                {!product.lastSale && (
                  <div className="text-center p-2 bg-warning/10 border border-warning/20 rounded-lg">
                    <p className="text-xs text-warning">
                      لم يتم بيع هذا المنتج بعد
                    </p>
                  </div>
                )}
              </div>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

export default Products;