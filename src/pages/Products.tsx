import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Plus, ArrowRight, Search, Package, Edit, Trash2, Save, X, Minus } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast as sonnerToast } from "sonner";
import { Link } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { useData } from "@/contexts/DataContext";
import { Textarea } from "@/components/ui/textarea";
import type { ProductMaterial } from "@/contexts/DataContext";

const Products = () => {
  const { toast } = useToast();
  const { products, addProduct, updateProduct, deleteProduct, materials } = useData();
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddingProduct, setIsAddingProduct] = useState(false);
  const [editingProduct, setEditingProduct] = useState<string | null>(null);
  const [newProduct, setNewProduct] = useState({
    name: "",
    defaultPrice: "",
    unit: "وحدة",
    description: "",
    materials: [] as ProductMaterial[],
  });
  const [editProduct, setEditProduct] = useState({
    name: "",
    defaultPrice: "",
    unit: "وحدة",
    description: "",
    materials: [] as ProductMaterial[],
  });

  // إضافة خامة جديدة للمنتج
  const addMaterialToProduct = (isEdit = false) => {
    const newMaterial: ProductMaterial = {
      materialId: "",
      materialName: "",
      quantityNeeded: 0,
    };

    if (isEdit) {
      setEditProduct(prev => ({
        ...prev,
        materials: [...prev.materials, newMaterial]
      }));
    } else {
      setNewProduct(prev => ({
        ...prev,
        materials: [...prev.materials, newMaterial]
      }));
    }
  };

  // إزالة خامة من المنتج
  const removeMaterialFromProduct = (index: number, isEdit = false) => {
    if (isEdit) {
      setEditProduct(prev => ({
        ...prev,
        materials: prev.materials.filter((_, i) => i !== index)
      }));
    } else {
      setNewProduct(prev => ({
        ...prev,
        materials: prev.materials.filter((_, i) => i !== index)
      }));
    }
  };

  // تحديث خامة في المنتج
  const updateMaterialInProduct = (index: number, field: keyof ProductMaterial, value: string | number, isEdit = false) => {
    if (isEdit) {
      setEditProduct(prev => ({
        ...prev,
        materials: prev.materials.map((material, i) =>
          i === index ? { ...material, [field]: value } : material
        )
      }));
    } else {
      setNewProduct(prev => ({
        ...prev,
        materials: prev.materials.map((material, i) =>
          i === index ? { ...material, [field]: value } : material
        )
      }));
    }
  };

  // تصفية المنتجات
  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // إضافة منتج جديد
  const handleAddProduct = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newProduct.name || !newProduct.defaultPrice) {
      toast({
        title: "خطأ في البيانات",
        description: "يرجى ملء اسم المنتج والسعر",
        variant: "destructive",
      });
      return;
    }

    try {
      const productData = {
        name: newProduct.name,
        defaultPrice: parseFloat(newProduct.defaultPrice),
        unit: newProduct.unit,
        description: newProduct.description,
        materials: newProduct.materials,
      };

      await addProduct(productData);
      setNewProduct({ name: "", defaultPrice: "", unit: "وحدة", description: "", materials: [] });
      setIsAddingProduct(false);

      toast({
        title: "تم إضافة المنتج بنجاح",
        description: `تم إضافة ${productData.name} إلى قائمة المنتجات`,
      });
    } catch (error) {
      toast({
        title: "خطأ في إضافة المنتج",
        description: "حدث خطأ أثناء إضافة المنتج",
        variant: "destructive",
      });
      console.error('Error adding product:', error);
    }
  };

  // بدء تعديل منتج
  const handleEditProduct = (product: any) => {
    setEditingProduct(product.id);
    setEditProduct({
      name: product.name,
      defaultPrice: product.defaultPrice.toString(),
      unit: product.unit,
      description: product.description,
      materials: product.materials || [],
    });
  };

  // حفظ تعديل المنتج
  const handleSaveEdit = async (productId: string) => {
    if (!editProduct.name || !editProduct.defaultPrice) {
      sonnerToast.error("يرجى ملء اسم المنتج والسعر");
      return;
    }

    try {
      const productData = {
        name: editProduct.name,
        defaultPrice: parseFloat(editProduct.defaultPrice),
        unit: editProduct.unit,
        description: editProduct.description,
        materials: editProduct.materials,
      };

      await updateProduct(productId, productData);
      setEditingProduct(null);
      sonnerToast.success("تم تحديث المنتج بنجاح");
    } catch (error) {
      sonnerToast.error("حدث خطأ أثناء تحديث المنتج");
      console.error('Error updating product:', error);
    }
  };

  // إلغاء التعديل
  const handleCancelEdit = () => {
    setEditingProduct(null);
    setEditProduct({ name: "", defaultPrice: "", unit: "وحدة", description: "", materials: [] });
  };

  // حذف منتج
  const handleDeleteProduct = async (productId: string, productName: string) => {
    if (window.confirm(`هل أنت متأكد من حذف المنتج "${productName}"؟`)) {
      try {
        await deleteProduct(productId);
        sonnerToast.success("تم حذف المنتج بنجاح");
      } catch (error) {
        sonnerToast.error("حدث خطأ أثناء حذف المنتج");
        console.error('Error deleting product:', error);
      }
    }
  };

  // إعادة تعيين النموذج
  const resetForm = () => {
    setNewProduct({ name: "", defaultPrice: "", unit: "وحدة", description: "" });
    setIsAddingProduct(false);
  };

  // حساب الإحصائيات
  const stats = {
    totalProducts: filteredProducts.length,
    totalRevenue: filteredProducts.reduce((sum, p) => sum + p.revenue, 0),
    totalQuantitySold: filteredProducts.reduce((sum, p) => sum + p.totalSold, 0),
    averagePrice: filteredProducts.length > 0 
      ? filteredProducts.reduce((sum, p) => sum + p.defaultPrice, 0) / filteredProducts.length 
      : 0,
  };

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link to="/">
            <Button variant="ghost" size="sm">
              <ArrowRight className="w-4 h-4" />
              العودة للرئيسية
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-foreground">إدارة المنتجات</h1>
            <p className="text-muted-foreground">قائمة المنتجات والأسعار</p>
          </div>
        </div>
        <Button 
          variant="gradient" 
          size="lg"
          onClick={() => setIsAddingProduct(true)}
        >
          <Plus className="w-5 h-5" />
          إضافة منتج جديد
        </Button>
      </div>

      {/* أدوات البحث والإحصائيات */}
      <Card className="p-6 shadow-card">
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between mb-6">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="ابحث عن المنتج..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pr-10"
            />
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-primary/5 rounded-lg">
            <p className="text-2xl font-bold text-primary numbers-ltr">{stats.totalProducts}</p>
            <p className="text-sm text-muted-foreground">إجمالي المنتجات</p>
          </div>
          <div className="text-center p-4 bg-success/5 rounded-lg">
            <p className="text-2xl font-bold text-success numbers-ltr">{stats.totalRevenue.toLocaleString()}</p>
            <p className="text-sm text-muted-foreground">إجمالي الإيرادات</p>
          </div>
          <div className="text-center p-4 bg-secondary-accent/5 rounded-lg">
            <p className="text-2xl font-bold text-secondary-accent numbers-ltr">{stats.totalQuantitySold}</p>
            <p className="text-sm text-muted-foreground">الكمية المباعة</p>
          </div>
          <div className="text-center p-4 bg-warning/5 rounded-lg">
            <p className="text-2xl font-bold text-warning numbers-ltr">{stats.averagePrice.toFixed(0)}</p>
            <p className="text-sm text-muted-foreground">متوسط السعر</p>
          </div>
        </div>
      </Card>

      {/* نموذج إضافة منتج */}
      {isAddingProduct && (
        <Card className="p-6 shadow-card">
          <h3 className="text-lg font-semibold mb-4 text-foreground">إضافة منتج جديد</h3>
          <form onSubmit={handleAddProduct} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="productName">اسم المنتج *</Label>
                <Input
                  id="productName"
                  placeholder="مثال: باذنجان"
                  value={newProduct.name}
                  onChange={(e) => setNewProduct(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="defaultPrice">السعر الافتراضي *</Label>
                <Input
                  id="defaultPrice"
                  type="number"
                  step="0.01"
                  placeholder="25.50"
                  value={newProduct.defaultPrice}
                  onChange={(e) => setNewProduct(prev => ({ ...prev, defaultPrice: e.target.value }))}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">وصف المنتج</Label>
              <Input
                id="description"
                placeholder="وصف مختصر للمنتج..."
                value={newProduct.description}
                onChange={(e) => setNewProduct(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>

            {/* إدارة الخامات المطلوبة */}
            <div className="space-y-4 border-t pt-4">
              <div className="flex justify-between items-center">
                <Label className="text-base font-medium">الخامات المطلوبة لصنع المنتج</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => addMaterialToProduct(false)}
                >
                  <Plus className="w-4 h-4" />
                  إضافة خامة
                </Button>
              </div>

              {newProduct.materials.map((material, index) => (
                <div key={index} className="grid grid-cols-1 md:grid-cols-4 gap-2 p-3 border rounded-lg">
                  <div className="space-y-1">
                    <Label className="text-sm">الخامة</Label>
                    <Select
                      value={material.materialId}
                      onValueChange={(value) => {
                        const selectedMaterial = materials.find(m => m.id === value);
                        updateMaterialInProduct(index, 'materialId', value, false);
                        updateMaterialInProduct(index, 'materialName', selectedMaterial?.name || '', false);
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="اختر خامة" />
                      </SelectTrigger>
                      <SelectContent>
                        {materials.map((mat) => (
                          <SelectItem key={mat.id} value={mat.id!}>
                            {mat.name} ({mat.currentStock} {mat.unit})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-1">
                    <Label className="text-sm">الكمية المطلوبة</Label>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="0"
                      value={material.quantityNeeded}
                      onChange={(e) => updateMaterialInProduct(index, 'quantityNeeded', parseFloat(e.target.value) || 0, false)}
                    />
                  </div>

                  <div className="space-y-1">
                    <Label className="text-sm">الوحدة</Label>
                    <Input
                      value={materials.find(m => m.id === material.materialId)?.unit || ''}
                      readOnly
                      className="bg-gray-100 dark:bg-gray-800"
                    />
                  </div>

                  <div className="space-y-1">
                    <Label className="text-sm">إجراء</Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeMaterialFromProduct(index, false)}
                      className="w-full"
                    >
                      <Minus className="w-4 h-4" />
                      حذف
                    </Button>
                  </div>
                </div>
              ))}

              {newProduct.materials.length === 0 && (
                <div className="text-center py-4 text-gray-500">
                  لم يتم إضافة خامات بعد. اضغط "إضافة خامة" لبدء إضافة الخامات المطلوبة.
                </div>
              )}
            </div>

            <div className="flex gap-4">
              <Button type="submit" variant="gradient">
                <Plus className="w-4 h-4" />
                حفظ المنتج
              </Button>
              <Button 
                type="button" 
                variant="outline"
                onClick={() => setIsAddingProduct(false)}
              >
                إلغاء
              </Button>
            </div>
          </form>
        </Card>
      )}

      {/* قائمة المنتجات */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredProducts.length === 0 ? (
          <Card className="col-span-full p-8 text-center shadow-card">
            <div className="space-y-4">
              {products.length === 0 ? (
                <>
                  <p className="text-muted-foreground text-lg">لا توجد منتجات مضافة حتى الآن</p>
                  <p className="text-sm text-muted-foreground">
                    ابدأ بإضافة المنتجات التي تتاجر بها
                  </p>
                  <Button
                    variant="gradient"
                    className="mt-4"
                    onClick={() => setIsAddingProduct(true)}
                  >
                    <Plus className="w-4 h-4 ml-2" />
                    إضافة أول منتج
                  </Button>
                </>
              ) : (
                <p className="text-muted-foreground">لا توجد منتجات مطابقة للبحث</p>
              )}
            </div>
          </Card>
        ) : (
          filteredProducts.map((product) => (
            <Card key={product.id} className="p-6 shadow-card transition-smooth hover:shadow-elevated">
              {editingProduct === product.id ? (
                /* نموذج التعديل */
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-foreground mb-4">تعديل المنتج</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor={`edit-name-${product.id}`}>اسم المنتج *</Label>
                      <Input
                        id={`edit-name-${product.id}`}
                        placeholder="اسم المنتج"
                        value={editProduct.name}
                        onChange={(e) => setEditProduct(prev => ({ ...prev, name: e.target.value }))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`edit-price-${product.id}`}>السعر الافتراضي *</Label>
                      <Input
                        id={`edit-price-${product.id}`}
                        type="number"
                        step="0.01"
                        placeholder="25.50"
                        value={editProduct.defaultPrice}
                        onChange={(e) => setEditProduct(prev => ({ ...prev, defaultPrice: e.target.value }))}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor={`edit-unit-${product.id}`}>الوحدة</Label>
                      <Select value={editProduct.unit} onValueChange={(value) => setEditProduct(prev => ({ ...prev, unit: value }))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="وحدة">وحدة</SelectItem>
                          <SelectItem value="كيلو">كيلو</SelectItem>
                          <SelectItem value="جرام">جرام</SelectItem>
                          <SelectItem value="قطعة">قطعة</SelectItem>
                          <SelectItem value="علبة">علبة</SelectItem>
                          <SelectItem value="كيس">كيس</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`edit-description-${product.id}`}>وصف المنتج</Label>
                      <Input
                        id={`edit-description-${product.id}`}
                        placeholder="وصف مختصر للمنتج..."
                        value={editProduct.description}
                        onChange={(e) => setEditProduct(prev => ({ ...prev, description: e.target.value }))}
                      />
                    </div>
                  </div>

                  {/* إدارة الخامات المطلوبة في التعديل */}
                  <div className="space-y-4 border-t pt-4">
                    <div className="flex justify-between items-center">
                      <Label className="text-base font-medium">الخامات المطلوبة لصنع المنتج</Label>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => addMaterialToProduct(true)}
                      >
                        <Plus className="w-4 h-4" />
                        إضافة خامة
                      </Button>
                    </div>

                    {editProduct.materials.map((material, index) => (
                      <div key={index} className="grid grid-cols-1 md:grid-cols-4 gap-2 p-3 border rounded-lg">
                        <div className="space-y-1">
                          <Label className="text-sm">الخامة</Label>
                          <Select
                            value={material.materialId}
                            onValueChange={(value) => {
                              const selectedMaterial = materials.find(m => m.id === value);
                              updateMaterialInProduct(index, 'materialId', value, true);
                              updateMaterialInProduct(index, 'materialName', selectedMaterial?.name || '', true);
                            }}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="اختر خامة" />
                            </SelectTrigger>
                            <SelectContent>
                              {materials.map((mat) => (
                                <SelectItem key={mat.id} value={mat.id!}>
                                  {mat.name} ({mat.currentStock} {mat.unit})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-1">
                          <Label className="text-sm">الكمية المطلوبة</Label>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="0"
                            value={material.quantityNeeded}
                            onChange={(e) => updateMaterialInProduct(index, 'quantityNeeded', parseFloat(e.target.value) || 0, true)}
                          />
                        </div>

                        <div className="space-y-1">
                          <Label className="text-sm">الوحدة</Label>
                          <Input
                            value={materials.find(m => m.id === material.materialId)?.unit || ''}
                            readOnly
                            className="bg-gray-100 dark:bg-gray-800"
                          />
                        </div>

                        <div className="space-y-1">
                          <Label className="text-sm">إجراء</Label>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeMaterialFromProduct(index, true)}
                            className="w-full"
                          >
                            <Minus className="w-4 h-4" />
                            حذف
                          </Button>
                        </div>
                      </div>
                    ))}

                    {editProduct.materials.length === 0 && (
                      <div className="text-center py-4 text-gray-500">
                        لم يتم إضافة خامات بعد. اضغط "إضافة خامة" لبدء إضافة الخامات المطلوبة.
                      </div>
                    )}
                  </div>

                  <div className="flex gap-2 pt-2 border-t border-border">
                    <Button
                      variant="gradient"
                      size="sm"
                      onClick={() => product.id && handleSaveEdit(product.id)}
                      className="flex-1"
                    >
                      <Save className="w-4 h-4" />
                      حفظ التغييرات
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCancelEdit}
                      className="flex-1"
                    >
                      <X className="w-4 h-4" />
                      إلغاء
                    </Button>
                  </div>
                </div>
              ) : (
                /* عرض المنتج العادي */
                <div className="space-y-4">
                  {/* معلومات المنتج */}
                  <div>
                    <h3 className="text-lg font-semibold text-foreground mb-1">
                      {product.name}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {product.description || "لا يوجد وصف"}
                    </p>
                  </div>

                {/* السعر */}
                <div className="flex items-center justify-between p-3 bg-primary/5 rounded-lg">
                  <span className="text-sm text-muted-foreground">السعر الافتراضي:</span>
                  <span className="text-xl font-bold text-primary numbers-ltr">
                    {product.defaultPrice} ج.م / {product.unit}
                  </span>
                </div>

                {/* الإحصائيات */}
                <div className="grid grid-cols-2 gap-3">
                  <div className="text-center p-3 bg-muted/50 rounded-lg">
                    <p className="text-sm text-muted-foreground">الكمية المباعة</p>
                    <p className="text-lg font-bold text-success numbers-ltr">
                      {product.totalSold} {product.unit}
                    </p>
                  </div>
                  <div className="text-center p-3 bg-muted/50 rounded-lg">
                    <p className="text-sm text-muted-foreground">إجمالي الإيرادات</p>
                    <p className="text-lg font-bold text-secondary-accent numbers-ltr">
                      {product.revenue.toLocaleString()} ج.م
                    </p>
                  </div>
                </div>

                {/* آخر بيع */}
                {product.lastSale && (
                  <div className="text-center p-2 bg-accent rounded-lg">
                    <p className="text-xs text-muted-foreground">
                      آخر بيع: {new Date(product.lastSale).toLocaleDateString('ar-EG', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                      })}
                    </p>
                  </div>
                )}

                {/* لا يوجد مبيعات */}
                {!product.lastSale && (
                  <div className="text-center p-2 bg-warning/10 border border-warning/20 rounded-lg">
                    <p className="text-xs text-warning">
                      لم يتم بيع هذا المنتج بعد
                    </p>
                  </div>
                )}

                {/* أزرار الإجراءات */}
                <div className="flex gap-2 pt-2 border-t border-border">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEditProduct(product)}
                    className="flex-1"
                  >
                    <Edit className="w-4 h-4" />
                    تعديل
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => product.id && handleDeleteProduct(product.id, product.name)}
                    className="flex-1 text-destructive hover:text-destructive"
                  >
                    <Trash2 className="w-4 h-4" />
                    حذف
                  </Button>
                </div>
                </div>
              )}
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

export default Products;