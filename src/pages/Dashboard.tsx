import React from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus, Calendar, FileText, Search } from "lucide-react";
import { Link } from "react-router-dom";
import heroImage from "@/assets/accounting-hero.jpg";

const Dashboard = () => {
  // بيانات وهمية للإحصائيات
  const stats = {
    todaySales: 15000,
    totalDebts: 45000,
    thisMonthSales: 450000,
    customersCount: 25,
  };

  const recentTransactions = [
    {
      id: 1,
      customerName: "متجر أحمد الزراعي",
      product: "طماطم",
      quantity: "50 كيلو",
      total: 2500,
      paid: 2000,
      remaining: 500,
      date: "2024-07-01",
    },
    {
      id: 2,
      customerName: "تجارة محمد للخضار",
      product: "خيار",
      quantity: "30 كيلو",
      total: 1800,
      paid: 1800,
      remaining: 0,
      date: "2024-07-01",
    },
    {
      id: 3,
      customerName: "سوق الفواكه المركزي",
      product: "عنب",
      quantity: "20 كيلو",
      total: 4000,
      paid: 3000,
      remaining: 1000,
      date: "2024-06-30",
    },
  ];

  return (
    <div className="space-y-8">
      {/* قسم الترحيب والصورة */}
      <Card className="overflow-hidden shadow-elevated">
        <div className="relative h-64 md:h-80">
          <img 
            src={heroImage} 
            alt="برنامج المحاسبة اليومي" 
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-primary/80 to-primary/60 flex items-center justify-center">
            <div className="text-center text-primary-foreground">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                مرحباً بك في برنامج المحاسبة اليومي
              </h2>
              <p className="text-lg md:text-xl mb-6 opacity-90">
                إدارة المبيعات والمعاملات التجارية بكل سهولة ووضوح
              </p>
              <p className="text-base opacity-75">
                اليوم هو {new Date().toLocaleDateString('ar-SA')}
              </p>
            </div>
          </div>
        </div>
      </Card>

      {/* بطاقات الإحصائيات */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6 shadow-card transition-smooth hover:shadow-elevated">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">مبيعات اليوم</p>
              <p className="text-2xl font-bold text-success numbers-ltr">
                {stats.todaySales.toLocaleString()} ج.م
              </p>
            </div>
            <div className="w-12 h-12 rounded-lg gradient-success flex items-center justify-center">
              <Calendar className="w-6 h-6 text-success-foreground" />
            </div>
          </div>
        </Card>

        <Card className="p-6 shadow-card transition-smooth hover:shadow-elevated">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">إجمالي الديون</p>
              <p className="text-2xl font-bold text-destructive numbers-ltr">
                {stats.totalDebts.toLocaleString()} ج.م
              </p>
            </div>
            <div className="w-12 h-12 rounded-lg bg-destructive flex items-center justify-center">
              <FileText className="w-6 h-6 text-destructive-foreground" />
            </div>
          </div>
        </Card>

        <Card className="p-6 shadow-card transition-smooth hover:shadow-elevated">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">مبيعات الشهر</p>
              <p className="text-2xl font-bold text-primary numbers-ltr">
                {stats.thisMonthSales.toLocaleString()} ج.م
              </p>
            </div>
            <div className="w-12 h-12 rounded-lg gradient-primary flex items-center justify-center">
              <Calendar className="w-6 h-6 text-primary-foreground" />
            </div>
          </div>
        </Card>

        <Card className="p-6 shadow-card transition-smooth hover:shadow-elevated">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">عدد التجار</p>
              <p className="text-2xl font-bold text-secondary-accent numbers-ltr">
                {stats.customersCount}
              </p>
            </div>
            <div className="w-12 h-12 rounded-lg bg-secondary-accent flex items-center justify-center">
              <Search className="w-6 h-6 text-primary-foreground" />
            </div>
          </div>
        </Card>
      </div>

      {/* الإجراءات السريعة */}
      <Card className="p-6 shadow-card">
        <h3 className="text-xl font-semibold mb-4 text-foreground">
          الإجراءات السريعة
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link to="/add-transaction">
            <Button variant="gradient" size="lg" className="w-full">
              <Plus className="w-5 h-5" />
              إضافة عملية بيع
            </Button>
          </Link>
          <Link to="/customers">
            <Button variant="outline" size="lg" className="w-full">
              <Search className="w-5 h-5" />
              إدارة التجار
            </Button>
          </Link>
          <Link to="/transactions">
            <Button variant="outline" size="lg" className="w-full">
              <FileText className="w-5 h-5" />
              دفتر المعاملات
            </Button>
          </Link>
          <Link to="/reports">
            <Button variant="outline" size="lg" className="w-full">
              <Calendar className="w-5 h-5" />
              التقارير المالية
            </Button>
          </Link>
        </div>
      </Card>

      {/* آخر المعاملات */}
      <Card className="p-6 shadow-card">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-semibold text-foreground">
            آخر المعاملات
          </h3>
          <Link to="/transactions">
            <Button variant="ghost" size="sm">
              عرض الكل
            </Button>
          </Link>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full table-rtl">
            <thead>
              <tr className="border-b border-border">
                <th className="text-right py-3 font-medium text-muted-foreground">
                  التاجر
                </th>
                <th className="text-right py-3 font-medium text-muted-foreground">
                  المنتج
                </th>
                <th className="text-right py-3 font-medium text-muted-foreground">
                  الكمية
                </th>
                <th className="text-right py-3 font-medium text-muted-foreground">
                  المبلغ الكلي
                </th>
                <th className="text-right py-3 font-medium text-muted-foreground">
                  المدفوع
                </th>
                <th className="text-right py-3 font-medium text-muted-foreground">
                  المتبقي
                </th>
                <th className="text-right py-3 font-medium text-muted-foreground">
                  التاريخ
                </th>
              </tr>
            </thead>
            <tbody>
              {recentTransactions.map((transaction) => (
                <tr key={transaction.id} className="border-b border-border/50">
                  <td className="py-4 font-medium">{transaction.customerName}</td>
                  <td className="py-4">{transaction.product}</td>
                  <td className="py-4">{transaction.quantity}</td>
                  <td className="py-4 numbers-ltr">
                    {transaction.total.toLocaleString()} ج.م
                  </td>
                  <td className="py-4 numbers-ltr text-success">
                    {transaction.paid.toLocaleString()} ج.م
                  </td>
                  <td className="py-4 numbers-ltr">
                    <span className={transaction.remaining > 0 ? "text-destructive" : "text-success"}>
                      {transaction.remaining.toLocaleString()} ج.م
                    </span>
                  </td>
                  <td className="py-4 text-muted-foreground">
                    {new Date(transaction.date).toLocaleDateString('ar-SA')}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};

export default Dashboard;