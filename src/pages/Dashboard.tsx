import React from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus, Calendar, FileText, Search } from "lucide-react";
import { Link } from "react-router-dom";
import { useData } from "@/contexts/DataContext";
import heroImage from "@/assets/accounting-hero.jpg";
import LiveDateTime from "@/components/LiveDateTime";

const Dashboard = () => {
  const { transactions, getStats } = useData();

  // الحصول على الإحصائيات الحقيقية
  const stats = getStats();

  // أحدث 5 معاملات
  const recentTransactions = transactions
    .sort((a, b) => new Date(b.date + ' ' + b.time).getTime() - new Date(a.date + ' ' + a.time).getTime())
    .slice(0, 5)
    .map(transaction => ({
      id: transaction.id,
      customerName: transaction.customerName,
      product: transaction.product,
      quantity: transaction.quantity + ' كيلو',
      total: transaction.totalAmount,
      paid: transaction.paidAmount,
      remaining: transaction.remainingAmount,
      date: transaction.date,
    }));

  return (
    <div className="space-y-8">
      {/* قسم الترحيب والصورة */}
      <Card className="overflow-hidden shadow-elevated">
        <div className="relative h-48 sm:h-64 md:h-80">
          <img
            src={heroImage}
            alt="برنامج المحاسبة اليومي"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-primary/80 to-primary/60 flex items-center justify-center p-4">
            <div className="text-center text-primary-foreground max-w-4xl">
              <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-2 sm:mb-4">
                مرحباً بك في برنامج المحاسبة اليومي
              </h2>
              <p className="text-sm sm:text-base md:text-lg lg:text-xl mb-4 sm:mb-6 opacity-90">
                إدارة المبيعات والمعاملات التجارية بكل سهولة ووضوح
              </p>
              <LiveDateTime />
            </div>
          </div>
        </div>
      </Card>

      {/* بطاقات الإحصائيات */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        <Card className="p-4 sm:p-6 shadow-card transition-smooth hover:shadow-elevated">
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm text-muted-foreground">مبيعات اليوم</p>
              <p className="text-lg sm:text-2xl font-bold text-success numbers-ltr truncate">
                {stats.todaySales.toLocaleString()} ج.م
              </p>
            </div>
            <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-lg gradient-success flex items-center justify-center flex-shrink-0">
              <Calendar className="w-5 h-5 sm:w-6 sm:h-6 text-success-foreground" />
            </div>
          </div>
        </Card>

        <Card className="p-4 sm:p-6 shadow-card transition-smooth hover:shadow-elevated">
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm text-muted-foreground">إجمالي الديون</p>
              <p className="text-lg sm:text-2xl font-bold text-destructive numbers-ltr truncate">
                {stats.totalDebts.toLocaleString()} ج.م
              </p>
            </div>
            <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-lg bg-destructive flex items-center justify-center flex-shrink-0">
              <FileText className="w-5 h-5 sm:w-6 sm:h-6 text-destructive-foreground" />
            </div>
          </div>
        </Card>

        <Card className="p-4 sm:p-6 shadow-card transition-smooth hover:shadow-elevated">
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm text-muted-foreground">مبيعات الشهر</p>
              <p className="text-lg sm:text-2xl font-bold text-primary numbers-ltr truncate">
                {stats.thisMonthSales.toLocaleString()} ج.م
              </p>
            </div>
            <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-lg gradient-primary flex items-center justify-center flex-shrink-0">
              <Calendar className="w-5 h-5 sm:w-6 sm:h-6 text-primary-foreground" />
            </div>
          </div>
        </Card>

        <Card className="p-4 sm:p-6 shadow-card transition-smooth hover:shadow-elevated">
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm text-muted-foreground">عدد التجار</p>
              <p className="text-lg sm:text-2xl font-bold text-secondary-accent numbers-ltr truncate">
                {stats.customersCount}
              </p>
            </div>
            <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-lg bg-secondary-accent flex items-center justify-center flex-shrink-0">
              <Search className="w-5 h-5 sm:w-6 sm:h-6 text-primary-foreground" />
            </div>
          </div>
        </Card>
      </div>

      {/* الإجراءات السريعة */}
      <Card className="p-4 sm:p-6 shadow-card">
        <h3 className="text-lg sm:text-xl font-semibold mb-3 sm:mb-4 text-foreground">
          الإجراءات السريعة
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
          <Link to="/add-transaction">
            <Button variant="gradient" size="lg" className="w-full text-sm sm:text-base">
              <Plus className="w-4 h-4 sm:w-5 sm:h-5" />
              إضافة عملية بيع
            </Button>
          </Link>
          <Link to="/customers">
            <Button variant="outline" size="lg" className="w-full text-sm sm:text-base">
              <Search className="w-4 h-4 sm:w-5 sm:h-5" />
              إدارة التجار
            </Button>
          </Link>
          <Link to="/transactions">
            <Button variant="outline" size="lg" className="w-full text-sm sm:text-base">
              <FileText className="w-4 h-4 sm:w-5 sm:h-5" />
              دفتر المعاملات
            </Button>
          </Link>
          <Link to="/reports">
            <Button variant="outline" size="lg" className="w-full text-sm sm:text-base">
              <Calendar className="w-4 h-4 sm:w-5 sm:h-5" />
              التقارير المالية
            </Button>
          </Link>
        </div>
      </Card>

      {/* آخر المعاملات */}
      <Card className="p-4 sm:p-6 shadow-card">
        <div className="flex items-center justify-between mb-3 sm:mb-4">
          <h3 className="text-lg sm:text-xl font-semibold text-foreground">
            آخر المعاملات
          </h3>
          <Link to="/transactions">
            <Button variant="ghost" size="sm" className="text-xs sm:text-sm">
              <span className="hidden sm:inline">عرض الكل</span>
              <span className="sm:hidden">الكل</span>
            </Button>
          </Link>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full table-rtl min-w-[600px]">
            <thead>
              <tr className="border-b border-border">
                <th className="text-right py-2 sm:py-3 px-2 sm:px-4 font-medium text-muted-foreground text-xs sm:text-sm">
                  التاجر
                </th>
                <th className="text-right py-2 sm:py-3 px-2 sm:px-4 font-medium text-muted-foreground text-xs sm:text-sm">
                  المنتج
                </th>
                <th className="text-right py-2 sm:py-3 px-2 sm:px-4 font-medium text-muted-foreground text-xs sm:text-sm">
                  الكمية
                </th>
                <th className="text-right py-2 sm:py-3 px-2 sm:px-4 font-medium text-muted-foreground text-xs sm:text-sm">
                  المبلغ الكلي
                </th>
                <th className="text-right py-2 sm:py-3 px-2 sm:px-4 font-medium text-muted-foreground text-xs sm:text-sm">
                  المدفوع
                </th>
                <th className="text-right py-2 sm:py-3 px-2 sm:px-4 font-medium text-muted-foreground text-xs sm:text-sm">
                  المتبقي
                </th>
                <th className="text-right py-2 sm:py-3 px-2 sm:px-4 font-medium text-muted-foreground text-xs sm:text-sm">
                  التاريخ
                </th>
              </tr>
            </thead>
            <tbody>
              {recentTransactions.length === 0 ? (
                <tr>
                  <td colSpan={7} className="py-8 text-center text-muted-foreground">
                    لا توجد معاملات حتى الآن
                    <br />
                    <Link to="/add-transaction" className="text-primary hover:underline">
                      ابدأ بإضافة أول معاملة
                    </Link>
                  </td>
                </tr>
              ) : (
                recentTransactions.map((transaction) => (
                  <tr key={transaction.id} className="border-b border-border/50">
                    <td className="py-2 sm:py-4 px-2 sm:px-4 font-medium text-xs sm:text-sm">{transaction.customerName}</td>
                    <td className="py-2 sm:py-4 px-2 sm:px-4 text-xs sm:text-sm">{transaction.product}</td>
                    <td className="py-2 sm:py-4 px-2 sm:px-4 text-xs sm:text-sm">{transaction.quantity}</td>
                    <td className="py-2 sm:py-4 px-2 sm:px-4 numbers-ltr text-xs sm:text-sm">
                      {transaction.total.toLocaleString()} ج.م
                    </td>
                    <td className="py-2 sm:py-4 px-2 sm:px-4 numbers-ltr text-success text-xs sm:text-sm">
                      {transaction.paid.toLocaleString()} ج.م
                    </td>
                    <td className="py-2 sm:py-4 px-2 sm:px-4 numbers-ltr text-xs sm:text-sm">
                      <span className={transaction.remaining > 0 ? "text-destructive" : "text-success"}>
                        {transaction.remaining.toLocaleString()} ج.م
                      </span>
                    </td>
                    <td className="py-2 sm:py-4 px-2 sm:px-4 text-muted-foreground text-xs sm:text-sm">
                      {new Date(transaction.date).toLocaleDateString('ar-EG', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                      })}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};

export default Dashboard;