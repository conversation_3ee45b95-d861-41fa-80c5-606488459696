import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Plus, Search, DollarSign, Edit, Trash2, Save, X, Calendar, User, Receipt, CreditCard } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast as sonnerToast } from "sonner";
import { useToast } from "@/hooks/use-toast";
import { useData } from "@/contexts/DataContext";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import type { Expense } from "@/contexts/DataContext";

const Expenses = () => {
  const { toast } = useToast();
  const { expenses, addExpense, updateExpense, deleteExpense } = useData();
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddingExpense, setIsAddingExpense] = useState(false);
  const [editingExpense, setEditingExpense] = useState<string | null>(null);
  const [filterCategory, setFilterCategory] = useState("all");
  const [filterStatus, setFilterStatus] = useState("all");
  const [newExpense, setNewExpense] = useState({
    title: "",
    category: "مصاريف إدارية",
    amount: "",
    description: "",
    recipient: "",
    paymentMethod: "نقدي",
    date: new Date().toISOString().split('T')[0],
    time: new Date().toLocaleTimeString('ar-EG'),
    status: "paid" as const,
    isAdvance: false,
    advanceReturnDate: "",
    notes: "",
  });
  const [editExpense, setEditExpense] = useState({
    title: "",
    category: "مصاريف إدارية",
    amount: "",
    description: "",
    recipient: "",
    paymentMethod: "نقدي",
    date: "",
    time: "",
    status: "paid" as const,
    isAdvance: false,
    advanceReturnDate: "",
    advanceReturned: false,
    notes: "",
  });

  // فئات المصروفات
  const expenseCategories = [
    "مصاريف إدارية",
    "نثريات",
    "سلف",
    "مصاريف تشغيلية",
    "صيانة",
    "مواصلات",
    "اتصالات",
    "كهرباء ومياه",
    "إيجار",
    "تأمينات",
    "ضرائب",
    "أخرى"
  ];

  // طرق الدفع
  const paymentMethods = [
    "نقدي",
    "بنك",
    "فيزا",
    "تحويل بنكي",
    "شيك",
    "محفظة إلكترونية"
  ];

  // تصفية المصروفات
  const filteredExpenses = expenses.filter(expense => {
    const matchesSearch = expense.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         expense.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (expense.recipient && expense.recipient.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = filterCategory === "all" || expense.category === filterCategory;
    const matchesStatus = filterStatus === "all" || expense.status === filterStatus;
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  // حساب الإحصائيات
  const getExpenseStats = () => {
    const today = new Date().toISOString().split('T')[0];
    const thisMonth = new Date().toISOString().slice(0, 7);
    
    const todayExpenses = expenses
      .filter(e => e.date === today && e.status === 'paid')
      .reduce((sum, e) => sum + e.amount, 0);
    
    const monthlyExpenses = expenses
      .filter(e => e.date.startsWith(thisMonth) && e.status === 'paid')
      .reduce((sum, e) => sum + e.amount, 0);
    
    const pendingExpenses = expenses
      .filter(e => e.status === 'pending')
      .reduce((sum, e) => sum + e.amount, 0);
    
    const totalAdvances = expenses
      .filter(e => e.isAdvance && !e.advanceReturned && e.status === 'paid')
      .reduce((sum, e) => sum + e.amount, 0);
    
    return {
      todayExpenses,
      monthlyExpenses,
      pendingExpenses,
      totalAdvances,
      totalExpenses: expenses.filter(e => e.status === 'paid').length
    };
  };

  const stats = getExpenseStats();

  // إضافة مصروف جديد
  const handleAddExpense = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newExpense.title || !newExpense.amount) {
      toast({
        title: "خطأ في البيانات",
        description: "يرجى ملء عنوان المصروف والمبلغ",
        variant: "destructive",
      });
      return;
    }

    try {
      const expenseData = {
        title: newExpense.title,
        category: newExpense.category,
        amount: parseFloat(newExpense.amount),
        description: newExpense.description,
        recipient: newExpense.recipient,
        paymentMethod: newExpense.paymentMethod,
        date: newExpense.date,
        time: newExpense.time,
        status: newExpense.status,
        isAdvance: newExpense.isAdvance,
        advanceReturnDate: newExpense.isAdvance ? newExpense.advanceReturnDate : undefined,
        advanceReturned: newExpense.isAdvance ? false : undefined,
        notes: newExpense.notes,
      };

      await addExpense(expenseData);
      setNewExpense({
        title: "",
        category: "مصاريف إدارية",
        amount: "",
        description: "",
        recipient: "",
        paymentMethod: "نقدي",
        date: new Date().toISOString().split('T')[0],
        time: new Date().toLocaleTimeString('ar-EG'),
        status: "paid",
        isAdvance: false,
        advanceReturnDate: "",
        notes: "",
      });
      setIsAddingExpense(false);

      toast({
        title: "تم إضافة المصروف بنجاح",
        description: `تم إضافة ${expenseData.title} بمبلغ ${expenseData.amount} جنيه مصري`,
      });
    } catch (error) {
      toast({
        title: "خطأ في إضافة المصروف",
        description: "حدث خطأ أثناء إضافة المصروف",
        variant: "destructive",
      });
      console.error('Error adding expense:', error);
    }
  };

  // بدء تعديل مصروف
  const handleEditExpense = (expense: Expense) => {
    setEditingExpense(expense.id!);
    setEditExpense({
      title: expense.title,
      category: expense.category,
      amount: expense.amount.toString(),
      description: expense.description || "",
      recipient: expense.recipient || "",
      paymentMethod: expense.paymentMethod,
      date: expense.date,
      time: expense.time,
      status: expense.status,
      isAdvance: expense.isAdvance,
      advanceReturnDate: expense.advanceReturnDate || "",
      advanceReturned: expense.advanceReturned || false,
      notes: expense.notes || "",
    });
  };

  // حفظ تعديل المصروف
  const handleSaveEdit = async (expenseId: string) => {
    if (!editExpense.title || !editExpense.amount) {
      sonnerToast.error("يرجى ملء عنوان المصروف والمبلغ");
      return;
    }

    try {
      const expenseData = {
        title: editExpense.title,
        category: editExpense.category,
        amount: parseFloat(editExpense.amount),
        description: editExpense.description,
        recipient: editExpense.recipient,
        paymentMethod: editExpense.paymentMethod,
        date: editExpense.date,
        time: editExpense.time,
        status: editExpense.status,
        isAdvance: editExpense.isAdvance,
        advanceReturnDate: editExpense.isAdvance ? editExpense.advanceReturnDate : undefined,
        advanceReturned: editExpense.advanceReturned,
        notes: editExpense.notes,
      };

      await updateExpense(expenseId, expenseData);
      setEditingExpense(null);
      sonnerToast.success("تم تحديث المصروف بنجاح");
    } catch (error) {
      sonnerToast.error("حدث خطأ أثناء تحديث المصروف");
      console.error('Error updating expense:', error);
    }
  };

  // إلغاء التعديل
  const handleCancelEdit = () => {
    setEditingExpense(null);
    setEditExpense({
      title: "",
      category: "مصاريف إدارية",
      amount: "",
      description: "",
      recipient: "",
      paymentMethod: "نقدي",
      date: "",
      time: "",
      status: "paid",
      isAdvance: false,
      advanceReturnDate: "",
      advanceReturned: false,
      notes: "",
    });
  };

  // حذف مصروف
  const handleDeleteExpense = async (expenseId: string, expenseTitle: string) => {
    if (window.confirm(`هل أنت متأكد من حذف المصروف "${expenseTitle}"؟`)) {
      try {
        await deleteExpense(expenseId);
        sonnerToast.success("تم حذف المصروف بنجاح");
      } catch (error) {
        sonnerToast.error("حدث خطأ أثناء حذف المصروف");
        console.error('Error deleting expense:', error);
      }
    }
  };

  // الحصول على لون الحالة
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'cancelled': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  // الحصول على نص الحالة
  const getStatusText = (status: string) => {
    switch (status) {
      case 'paid': return 'مدفوع';
      case 'pending': return 'معلق';
      case 'cancelled': return 'ملغي';
      default: return status;
    }
  };

  return (
    <div className="space-y-6 p-4 md:p-6">
      {/* العنوان والإحصائيات */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold text-foreground">إدارة المصروفات</h1>
          <p className="text-muted-foreground">إدارة المصروفات والسلف والنثريات</p>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 w-full md:w-auto">
          <Card className="p-3">
            <div className="text-center">
              <DollarSign className="w-6 h-6 mx-auto mb-1 text-blue-600" />
              <p className="text-xs text-muted-foreground">اليوم</p>
              <p className="font-bold text-sm">{stats.todayExpenses.toFixed(2)} ج.م</p>
            </div>
          </Card>
          <Card className="p-3">
            <div className="text-center">
              <Calendar className="w-6 h-6 mx-auto mb-1 text-green-600" />
              <p className="text-xs text-muted-foreground">هذا الشهر</p>
              <p className="font-bold text-sm">{stats.monthlyExpenses.toFixed(2)} ج.م</p>
            </div>
          </Card>
          <Card className="p-3">
            <div className="text-center">
              <Receipt className="w-6 h-6 mx-auto mb-1 text-yellow-600" />
              <p className="text-xs text-muted-foreground">معلق</p>
              <p className="font-bold text-sm">{stats.pendingExpenses.toFixed(2)} ج.م</p>
            </div>
          </Card>
          <Card className="p-3">
            <div className="text-center">
              <User className="w-6 h-6 mx-auto mb-1 text-red-600" />
              <p className="text-xs text-muted-foreground">سلف</p>
              <p className="font-bold text-sm">{stats.totalAdvances.toFixed(2)} ج.م</p>
            </div>
          </Card>
        </div>
      </div>

      {/* أدوات التحكم */}
      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-2 flex-1">
          <div className="relative flex-1">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="البحث في المصروفات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pr-10"
            />
          </div>
          <Select value={filterCategory} onValueChange={setFilterCategory}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder="فلترة حسب الفئة" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع الفئات</SelectItem>
              {expenseCategories.map((category) => (
                <SelectItem key={category} value={category}>{category}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-full sm:w-32">
              <SelectValue placeholder="الحالة" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع الحالات</SelectItem>
              <SelectItem value="paid">مدفوع</SelectItem>
              <SelectItem value="pending">معلق</SelectItem>
              <SelectItem value="cancelled">ملغي</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Button
          onClick={() => setIsAddingExpense(true)}
          variant="gradient"
          className="w-full sm:w-auto"
        >
          <Plus className="w-4 h-4" />
          إضافة مصروف جديد
        </Button>
      </div>

      {/* نموذج إضافة مصروف */}
      {isAddingExpense && (
        <Card className="p-6 shadow-card">
          <h3 className="text-lg font-semibold mb-4 text-foreground">إضافة مصروف جديد</h3>
          <form onSubmit={handleAddExpense} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="expenseTitle">عنوان المصروف *</Label>
                <Input
                  id="expenseTitle"
                  placeholder="مثال: فاتورة كهرباء"
                  value={newExpense.title}
                  onChange={(e) => setNewExpense(prev => ({ ...prev, title: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="expenseAmount">المبلغ *</Label>
                <Input
                  id="expenseAmount"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  value={newExpense.amount}
                  onChange={(e) => setNewExpense(prev => ({ ...prev, amount: e.target.value }))}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="expenseCategory">فئة المصروف</Label>
                <Select value={newExpense.category} onValueChange={(value) => setNewExpense(prev => ({ ...prev, category: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {expenseCategories.map((category) => (
                      <SelectItem key={category} value={category}>{category}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="paymentMethod">طريقة الدفع</Label>
                <Select value={newExpense.paymentMethod} onValueChange={(value) => setNewExpense(prev => ({ ...prev, paymentMethod: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {paymentMethods.map((method) => (
                      <SelectItem key={method} value={method}>{method}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="expenseDate">التاريخ</Label>
                <Input
                  id="expenseDate"
                  type="date"
                  value={newExpense.date}
                  onChange={(e) => setNewExpense(prev => ({ ...prev, date: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="expenseStatus">الحالة</Label>
                <Select value={newExpense.status} onValueChange={(value: any) => setNewExpense(prev => ({ ...prev, status: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="paid">مدفوع</SelectItem>
                    <SelectItem value="pending">معلق</SelectItem>
                    <SelectItem value="cancelled">ملغي</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="expenseDescription">وصف المصروف</Label>
              <Textarea
                id="expenseDescription"
                placeholder="تفاصيل إضافية عن المصروف..."
                value={newExpense.description}
                onChange={(e) => setNewExpense(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
              />
            </div>

            {/* خيارات السلفة */}
            <div className="space-y-4 border-t pt-4">
              <div className="flex items-center space-x-2 space-x-reverse">
                <input
                  type="checkbox"
                  id="isAdvance"
                  checked={newExpense.isAdvance}
                  onChange={(e) => setNewExpense(prev => ({ ...prev, isAdvance: e.target.checked }))}
                  className="rounded"
                />
                <Label htmlFor="isAdvance">هذا المصروف عبارة عن سلفة</Label>
              </div>

              {newExpense.isAdvance && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="recipient">المستلم</Label>
                    <Input
                      id="recipient"
                      placeholder="اسم الشخص المستلم للسلفة"
                      value={newExpense.recipient}
                      onChange={(e) => setNewExpense(prev => ({ ...prev, recipient: e.target.value }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="advanceReturnDate">تاريخ الاسترداد المتوقع</Label>
                    <Input
                      id="advanceReturnDate"
                      type="date"
                      value={newExpense.advanceReturnDate}
                      onChange={(e) => setNewExpense(prev => ({ ...prev, advanceReturnDate: e.target.value }))}
                    />
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="expenseNotes">ملاحظات</Label>
              <Textarea
                id="expenseNotes"
                placeholder="ملاحظات إضافية..."
                value={newExpense.notes}
                onChange={(e) => setNewExpense(prev => ({ ...prev, notes: e.target.value }))}
                rows={2}
              />
            </div>

            <div className="flex gap-4">
              <Button type="submit" variant="gradient">
                <Plus className="w-4 h-4" />
                حفظ المصروف
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsAddingExpense(false)}
              >
                إلغاء
              </Button>
            </div>
          </form>
        </Card>
      )}

      {/* قائمة المصروفات */}
      <Card className="shadow-card">
        <div className="p-6">
          <h3 className="text-lg font-semibold mb-4 text-foreground">
            قائمة المصروفات ({filteredExpenses.length})
          </h3>

          {filteredExpenses.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <DollarSign className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>لا توجد مصروفات مطابقة للبحث</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredExpenses.map((expense) => (
                <div key={expense.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                  {editingExpense === expense.id ? (
                    // نموذج التعديل
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>عنوان المصروف</Label>
                          <Input
                            value={editExpense.title}
                            onChange={(e) => setEditExpense(prev => ({ ...prev, title: e.target.value }))}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>المبلغ</Label>
                          <Input
                            type="number"
                            step="0.01"
                            value={editExpense.amount}
                            onChange={(e) => setEditExpense(prev => ({ ...prev, amount: e.target.value }))}
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>فئة المصروف</Label>
                          <Select value={editExpense.category} onValueChange={(value) => setEditExpense(prev => ({ ...prev, category: value }))}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {expenseCategories.map((category) => (
                                <SelectItem key={category} value={category}>{category}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label>طريقة الدفع</Label>
                          <Select value={editExpense.paymentMethod} onValueChange={(value) => setEditExpense(prev => ({ ...prev, paymentMethod: value }))}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {paymentMethods.map((method) => (
                                <SelectItem key={method} value={method}>{method}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>التاريخ</Label>
                          <Input
                            type="date"
                            value={editExpense.date}
                            onChange={(e) => setEditExpense(prev => ({ ...prev, date: e.target.value }))}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>الحالة</Label>
                          <Select value={editExpense.status} onValueChange={(value: any) => setEditExpense(prev => ({ ...prev, status: value }))}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="paid">مدفوع</SelectItem>
                              <SelectItem value="pending">معلق</SelectItem>
                              <SelectItem value="cancelled">ملغي</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      {editExpense.isAdvance && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label>المستلم</Label>
                            <Input
                              value={editExpense.recipient}
                              onChange={(e) => setEditExpense(prev => ({ ...prev, recipient: e.target.value }))}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>تاريخ الاسترداد</Label>
                            <Input
                              type="date"
                              value={editExpense.advanceReturnDate}
                              onChange={(e) => setEditExpense(prev => ({ ...prev, advanceReturnDate: e.target.value }))}
                            />
                          </div>
                        </div>
                      )}

                      <div className="space-y-2">
                        <Label>الوصف</Label>
                        <Textarea
                          value={editExpense.description}
                          onChange={(e) => setEditExpense(prev => ({ ...prev, description: e.target.value }))}
                          rows={2}
                        />
                      </div>

                      <div className="flex gap-2">
                        <Button
                          onClick={() => handleSaveEdit(expense.id!)}
                          size="sm"
                          variant="gradient"
                        >
                          <Save className="w-4 h-4" />
                          حفظ
                        </Button>
                        <Button
                          onClick={handleCancelEdit}
                          size="sm"
                          variant="outline"
                        >
                          <X className="w-4 h-4" />
                          إلغاء
                        </Button>
                      </div>
                    </div>
                  ) : (
                    // عرض المصروف
                    <div className="space-y-3">
                      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-2">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-semibold text-foreground">{expense.title}</h4>
                            <Badge className={getStatusColor(expense.status)}>
                              {getStatusText(expense.status)}
                            </Badge>
                            {expense.isAdvance && (
                              <Badge variant="outline" className="text-blue-600 border-blue-600">
                                سلفة
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground">{expense.category}</p>
                        </div>

                        <div className="flex items-center gap-2">
                          <div className="text-left">
                            <p className="font-bold text-lg text-foreground">{expense.amount.toFixed(2)} ج.م</p>
                            <p className="text-xs text-muted-foreground">{expense.paymentMethod}</p>
                          </div>
                          <div className="flex gap-1">
                            <Button
                              onClick={() => handleEditExpense(expense)}
                              size="sm"
                              variant="outline"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              onClick={() => handleDeleteExpense(expense.id!, expense.title)}
                              size="sm"
                              variant="outline"
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-muted-foreground">التاريخ: </span>
                          <span className="text-foreground">{expense.date}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">الوقت: </span>
                          <span className="text-foreground">{expense.time}</span>
                        </div>
                        {expense.recipient && (
                          <div>
                            <span className="text-muted-foreground">المستلم: </span>
                            <span className="text-foreground">{expense.recipient}</span>
                          </div>
                        )}
                      </div>

                      {expense.description && (
                        <div className="text-sm">
                          <span className="text-muted-foreground">الوصف: </span>
                          <span className="text-foreground">{expense.description}</span>
                        </div>
                      )}

                      {expense.isAdvance && expense.advanceReturnDate && (
                        <div className="text-sm">
                          <span className="text-muted-foreground">تاريخ الاسترداد المتوقع: </span>
                          <span className="text-foreground">{expense.advanceReturnDate}</span>
                          {expense.advanceReturned && (
                            <Badge className="mr-2 bg-green-100 text-green-800">تم الاسترداد</Badge>
                          )}
                        </div>
                      )}

                      {expense.notes && (
                        <div className="text-sm">
                          <span className="text-muted-foreground">ملاحظات: </span>
                          <span className="text-foreground">{expense.notes}</span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default Expenses;
