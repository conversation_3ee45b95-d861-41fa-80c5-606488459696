import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Search, ArrowRight, FileText, Plus, Users, Edit, Trash2, Eye, Save, X, Phone, MapPin } from "lucide-react";
import { toast } from "sonner";
import { Link } from "react-router-dom";
import { useData } from "@/contexts/DataContext";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

const Customers = () => {
  const { customers, deleteCustomer, updateCustomer, transactions } = useData();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);
  const [editingCustomer, setEditingCustomer] = useState<string | null>(null);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [editForm, setEditForm] = useState({
    name: "",
    phone: "",
    address: "",
    notes: ""
  });

  // تصفية العملاء
  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // وظائف التعامل مع العملاء
  const handleViewDetails = (customer: any) => {
    setSelectedCustomer(customer);
    setShowDetailsDialog(true);
  };

  const handleEditCustomer = (customer: any) => {
    setEditingCustomer(customer.id || null);
    setEditForm({
      name: customer.name,
      phone: customer.phone || "",
      address: customer.address || "",
      notes: customer.notes || ""
    });
  };

  const handleSaveEdit = async (customerId: string) => {
    if (!editForm.name.trim()) {
      toast.error("يرجى إدخال اسم العميل");
      return;
    }

    const existingCustomer = customers.find(c => c.id === customerId);
    if (!existingCustomer) return;

    try {
      const updatedCustomer = {
        ...existingCustomer,
        name: editForm.name.trim(),
        phone: editForm.phone.trim(),
        address: editForm.address.trim(),
        notes: editForm.notes.trim()
      };

      await updateCustomer(updatedCustomer);
      setEditingCustomer(null);
      setEditForm({ name: "", phone: "", address: "", notes: "" });
      toast.success("تم تحديث بيانات العميل بنجاح");
    } catch (error) {
      toast.error("حدث خطأ أثناء تحديث بيانات العميل");
      console.error('Error updating customer:', error);
    }
  };

  const handleCancelEdit = () => {
    setEditingCustomer(null);
    setEditForm({ name: "", phone: "", address: "", notes: "" });
  };

  const handleDeleteCustomer = async (customerId: string, customerName: string) => {
    if (window.confirm(`هل أنت متأكد من حذف العميل "${customerName}"؟\nسيتم حذف جميع بياناته نهائياً.`)) {
      try {
        await deleteCustomer(customerId);
        toast.success("تم حذف العميل بنجاح");
      } catch (error) {
        toast.error("حدث خطأ أثناء حذف العميل");
        console.error('Error deleting customer:', error);
      }
    }
  };

  // الحصول على معاملات عميل معين
  const getCustomerTransactions = (customerName: string) => {
    return transactions.filter(t => t.customerName === customerName);
  };

  // حساب الإحصائيات
  const stats = {
    totalCustomers: filteredCustomers.length,
    totalDebts: filteredCustomers.reduce((sum, c) => sum + c.remainingDebt, 0),
    totalSales: filteredCustomers.reduce((sum, c) => sum + c.totalPurchases, 0),
    customersWithDebt: filteredCustomers.filter(c => c.remainingDebt > 0).length,
  };

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link to="/">
            <Button variant="ghost" size="sm">
              <ArrowRight className="w-4 h-4" />
              العودة
            </Button>
          </Link>
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Users className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-foreground">إدارة التجار</h1>
              <p className="text-sm text-muted-foreground">
                عرض وإدارة بيانات التجار والعملاء
              </p>
            </div>
          </div>
        </div>
        <div className="flex gap-3">
          <Link to="/add-transaction">
            <Button variant="gradient">
              <Plus className="w-4 h-4 ml-2" />
              إضافة معاملة جديدة
            </Button>
          </Link>
          <Link to="/reports">
            <Button variant="outline">
              <FileText className="w-4 h-4 ml-2" />
              التقارير
            </Button>
          </Link>
        </div>
      </div>

      {/* شريط البحث */}
      <Card className="p-4 shadow-card">
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="البحث عن تاجر..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pr-10"
          />
        </div>
      </Card>

      {/* الإحصائيات */}
      <Card className="p-6 shadow-card">
        <h2 className="text-lg font-semibold mb-4 text-foreground">إحصائيات التجار</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-primary/5 rounded-lg">
            <p className="text-2xl font-bold text-primary numbers-ltr">{stats.totalCustomers}</p>
            <p className="text-sm text-muted-foreground">إجمالي التجار</p>
          </div>
          <div className="text-center p-4 bg-success/5 rounded-lg">
            <p className="text-2xl font-bold text-success numbers-ltr">{stats.totalSales.toLocaleString()}</p>
            <p className="text-sm text-muted-foreground">إجمالي المبيعات</p>
          </div>
          <div className="text-center p-4 bg-destructive/5 rounded-lg">
            <p className="text-2xl font-bold text-destructive numbers-ltr">{stats.totalDebts.toLocaleString()}</p>
            <p className="text-sm text-muted-foreground">إجمالي الديون</p>
          </div>
          <div className="text-center p-4 bg-warning/5 rounded-lg">
            <p className="text-2xl font-bold text-warning numbers-ltr">{stats.customersWithDebt}</p>
            <p className="text-sm text-muted-foreground">عليهم ديون</p>
          </div>
        </div>
      </Card>

      {/* قائمة العملاء */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredCustomers.length === 0 ? (
          <Card className="col-span-full p-8 text-center shadow-card">
            <div className="space-y-4">
              {customers.length === 0 ? (
                <>
                  <p className="text-muted-foreground text-lg">لا يوجد تجار مسجلين حتى الآن</p>
                  <p className="text-sm text-muted-foreground">
                    سيتم إضافة التجار تلقائياً عند إضافة أول معاملة لهم
                  </p>
                  <Link to="/add-transaction">
                    <Button variant="gradient" className="mt-4">
                      <Plus className="w-4 h-4 ml-2" />
                      إضافة أول معاملة
                    </Button>
                  </Link>
                </>
              ) : (
                <p className="text-muted-foreground">لا توجد نتائج مطابقة للبحث</p>
              )}
            </div>
          </Card>
        ) : (
          filteredCustomers.map((customer) => (
            <Card key={customer.id} className="p-6 shadow-card transition-smooth hover:shadow-elevated">
              {editingCustomer === customer.id ? (
                /* نموذج التعديل */
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-foreground mb-4">تعديل بيانات العميل</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor={`edit-name-${customer.id}`}>اسم العميل *</Label>
                      <Input
                        id={`edit-name-${customer.id}`}
                        placeholder="اسم العميل"
                        value={editForm.name}
                        onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`edit-phone-${customer.id}`}>رقم الهاتف</Label>
                      <Input
                        id={`edit-phone-${customer.id}`}
                        placeholder="01234567890"
                        value={editForm.phone}
                        onChange={(e) => setEditForm(prev => ({ ...prev, phone: e.target.value }))}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`edit-address-${customer.id}`}>العنوان</Label>
                    <Input
                      id={`edit-address-${customer.id}`}
                      placeholder="عنوان العميل"
                      value={editForm.address}
                      onChange={(e) => setEditForm(prev => ({ ...prev, address: e.target.value }))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`edit-notes-${customer.id}`}>ملاحظات</Label>
                    <Textarea
                      id={`edit-notes-${customer.id}`}
                      placeholder="ملاحظات إضافية..."
                      value={editForm.notes}
                      onChange={(e) => setEditForm(prev => ({ ...prev, notes: e.target.value }))}
                      rows={3}
                    />
                  </div>

                  <div className="flex gap-2 pt-2 border-t border-border">
                    <Button
                      variant="gradient"
                      size="sm"
                      onClick={() => handleSaveEdit(customer.id)}
                      className="flex-1"
                    >
                      <Save className="w-4 h-4" />
                      حفظ التغييرات
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCancelEdit}
                      className="flex-1"
                    >
                      <X className="w-4 h-4" />
                      إلغاء
                    </Button>
                  </div>
                </div>
              ) : (
                /* عرض العميل العادي */
                <div className="space-y-4">
                  {/* معلومات العميل */}
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-foreground mb-1">
                        {customer.name}
                      </h3>
                      <div className="space-y-1">
                        {customer.phone && (
                          <p className="text-sm text-muted-foreground flex items-center gap-2">
                            <Phone className="w-4 h-4" />
                            {customer.phone}
                          </p>
                        )}
                        {customer.address && (
                          <p className="text-sm text-muted-foreground flex items-center gap-2">
                            <MapPin className="w-4 h-4" />
                            {customer.address}
                          </p>
                        )}
                        <p className="text-sm text-muted-foreground">
                          آخر معاملة: {new Date(customer.lastTransaction).toLocaleDateString('ar-EG', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                          })}
                        </p>
                      </div>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewDetails(customer)}
                      >
                        <Eye className="w-4 h-4" />
                        التفاصيل
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditCustomer(customer)}
                      >
                        <Edit className="w-4 h-4" />
                        تعديل
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteCustomer(customer.id, customer.name)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="w-4 h-4" />
                        حذف
                      </Button>
                    </div>
                  </div>

                  {/* الإحصائيات */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-muted/50 rounded-lg">
                      <p className="text-sm text-muted-foreground">عدد المعاملات</p>
                      <p className="text-xl font-bold text-primary numbers-ltr">
                        {customer.totalTransactions}
                      </p>
                    </div>
                    <div className="text-center p-3 bg-muted/50 rounded-lg">
                      <p className="text-sm text-muted-foreground">إجمالي المشتريات</p>
                      <p className="text-xl font-bold text-success numbers-ltr">
                        {customer.totalPurchases.toLocaleString()}
                      </p>
                    </div>
                  </div>

                  {/* حالة الدفع */}
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">المبلغ المدفوع:</span>
                      <span className="font-medium text-success numbers-ltr">
                        {customer.totalPaid.toLocaleString()} ج.م
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">المبلغ المتبقي:</span>
                      <span className={`font-bold numbers-ltr ${customer.remainingDebt > 0 ? "text-destructive" : "text-success"}`}>
                        {customer.remainingDebt.toLocaleString()} ج.م
                      </span>
                    </div>

                    {/* شريط التقدم */}
                    <div className="w-full bg-muted rounded-full h-2">
                      <div
                        className="bg-success h-2 rounded-full transition-smooth"
                        style={{
                          width: `${(customer.totalPaid / customer.totalPurchases) * 100}%`
                        }}
                      ></div>
                    </div>
                    <p className="text-xs text-muted-foreground text-center">
                      {((customer.totalPaid / customer.totalPurchases) * 100).toFixed(1)}% مدفوع
                    </p>
                  </div>

                  {/* حالة الدين */}
                  {customer.remainingDebt > 0 && (
                    <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
                      <p className="text-sm text-destructive font-medium">
                        يوجد دين مستحق: {customer.remainingDebt.toLocaleString()} ج.م
                      </p>
                    </div>
                  )}
                </div>
              )}
            </Card>
          ))
        )}
      </div>

      {/* نافذة تفاصيل العميل */}
      <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold">
              تفاصيل العميل: {selectedCustomer?.name}
            </DialogTitle>
          </DialogHeader>

          {selectedCustomer && (
            <div className="space-y-6">
              {/* معلومات العميل الأساسية */}
              <Card className="p-4">
                <h3 className="text-lg font-semibold mb-4">المعلومات الأساسية</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">الاسم:</p>
                    <p className="font-medium">{selectedCustomer.name}</p>
                  </div>
                  {selectedCustomer.phone && (
                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground">رقم الهاتف:</p>
                      <p className="font-medium flex items-center gap-2">
                        <Phone className="w-4 h-4" />
                        {selectedCustomer.phone}
                      </p>
                    </div>
                  )}
                  {selectedCustomer.address && (
                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground">العنوان:</p>
                      <p className="font-medium flex items-center gap-2">
                        <MapPin className="w-4 h-4" />
                        {selectedCustomer.address}
                      </p>
                    </div>
                  )}
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">آخر معاملة:</p>
                    <p className="font-medium">
                      {new Date(selectedCustomer.lastTransaction).toLocaleDateString('ar-EG', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </p>
                  </div>
                </div>
                {selectedCustomer.notes && (
                  <div className="mt-4 space-y-2">
                    <p className="text-sm text-muted-foreground">ملاحظات:</p>
                    <p className="font-medium bg-muted/50 p-3 rounded-lg">{selectedCustomer.notes}</p>
                  </div>
                )}
              </Card>

              {/* الإحصائيات المالية */}
              <Card className="p-4">
                <h3 className="text-lg font-semibold mb-4">الإحصائيات المالية</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-primary/5 rounded-lg">
                    <p className="text-2xl font-bold text-primary numbers-ltr">
                      {selectedCustomer.totalTransactions}
                    </p>
                    <p className="text-sm text-muted-foreground">عدد المعاملات</p>
                  </div>
                  <div className="text-center p-3 bg-success/5 rounded-lg">
                    <p className="text-2xl font-bold text-success numbers-ltr">
                      {selectedCustomer.totalPurchases.toLocaleString()} ج.م
                    </p>
                    <p className="text-sm text-muted-foreground">إجمالي المشتريات</p>
                  </div>
                  <div className="text-center p-3 bg-success/5 rounded-lg">
                    <p className="text-2xl font-bold text-success numbers-ltr">
                      {selectedCustomer.totalPaid.toLocaleString()} ج.م
                    </p>
                    <p className="text-sm text-muted-foreground">المبلغ المدفوع</p>
                  </div>
                  <div className="text-center p-3 bg-destructive/5 rounded-lg">
                    <p className="text-2xl font-bold text-destructive numbers-ltr">
                      {selectedCustomer.remainingDebt.toLocaleString()} ج.م
                    </p>
                    <p className="text-sm text-muted-foreground">المبلغ المتبقي</p>
                  </div>
                </div>

                {/* شريط التقدم */}
                <div className="mt-4 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>نسبة السداد</span>
                    <span className="numbers-ltr">
                      {((selectedCustomer.totalPaid / selectedCustomer.totalPurchases) * 100).toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-3">
                    <div
                      className="bg-success h-3 rounded-full transition-smooth"
                      style={{
                        width: `${(selectedCustomer.totalPaid / selectedCustomer.totalPurchases) * 100}%`
                      }}
                    ></div>
                  </div>
                </div>
              </Card>

              {/* معاملات العميل */}
              <Card className="p-4">
                <h3 className="text-lg font-semibold mb-4">
                  معاملات العميل ({getCustomerTransactions(selectedCustomer.name).length} معاملة)
                </h3>
                {getCustomerTransactions(selectedCustomer.name).length > 0 ? (
                  <div className="space-y-3 max-h-60 overflow-y-auto">
                    {getCustomerTransactions(selectedCustomer.name)
                      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                      .map((transaction) => (
                      <div key={transaction.id} className="p-3 bg-muted/30 rounded-lg border">
                        <div className="flex justify-between items-start">
                          <div className="space-y-1">
                            <p className="font-medium">{transaction.product}</p>
                            <p className="text-sm text-muted-foreground">
                              الكمية: {transaction.quantity} كيلو |
                              السعر: {transaction.pricePerUnit} ج.م/كيلو
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {new Date(transaction.date).toLocaleDateString('ar-EG')} - {transaction.time}
                            </p>
                          </div>
                          <div className="text-left space-y-1">
                            <p className="font-bold text-primary numbers-ltr">
                              {transaction.totalAmount.toLocaleString()} ج.م
                            </p>
                            <p className="text-sm text-success numbers-ltr">
                              مدفوع: {transaction.paidAmount.toLocaleString()} ج.م
                            </p>
                            {transaction.remainingAmount > 0 && (
                              <p className="text-sm text-destructive numbers-ltr">
                                متبقي: {transaction.remainingAmount.toLocaleString()} ج.م
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-center text-muted-foreground py-8">
                    لا توجد معاملات لهذا العميل
                  </p>
                )}
              </Card>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Customers;
