import React, { useState, useMemo } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar, FileText, ArrowRight, TrendingUp, Users, Package } from "lucide-react";
import { Link } from "react-router-dom";
import { useData } from "@/contexts/DataContext";
import { toast } from "sonner";
import ResponsiveTable from "@/components/ResponsiveTable";

const Reports = () => {
  const { transactions } = useData();
  const [dateFrom, setDateFrom] = useState("");
  const [dateTo, setDateTo] = useState("");
  const [reportType, setReportType] = useState("custom");
  const [reportGenerated, setReportGenerated] = useState(false);

  // حساب البيانات بناءً على التواريخ المحددة
  const reportData = useMemo(() => {
    if (!reportGenerated || (!dateFrom && !dateTo)) {
      return {
        totalSales: 0,
        totalTransactions: 0,
        totalPaid: 0,
        totalRemaining: 0,
        topProducts: [] as Array<{ name: string; quantity: number; revenue: number }>,
        topCustomers: [] as Array<{ name: string; transactions: number; total: number }>,
        filteredTransactions: [] as any[],
      };
    }

    // تصفية المعاملات حسب التاريخ
    let filteredTransactions = transactions;

    if (dateFrom) {
      filteredTransactions = filteredTransactions.filter(t =>
        new Date(t.date) >= new Date(dateFrom)
      );
    }

    if (dateTo) {
      filteredTransactions = filteredTransactions.filter(t =>
        new Date(t.date) <= new Date(dateTo)
      );
    }

    // حساب الإحصائيات الأساسية
    const totalSales = filteredTransactions.reduce((sum, t) => sum + t.totalAmount, 0);
    const totalTransactions = filteredTransactions.length;
    const totalPaid = filteredTransactions.reduce((sum, t) => sum + t.paidAmount, 0);
    const totalRemaining = filteredTransactions.reduce((sum, t) => sum + t.remainingAmount, 0);

    // حساب أفضل المنتجات
    const productStats = filteredTransactions.reduce((acc, t) => {
      if (!acc[t.product]) {
        acc[t.product] = { quantity: 0, revenue: 0 };
      }
      acc[t.product].quantity += parseFloat(t.quantity);
      acc[t.product].revenue += t.totalAmount;
      return acc;
    }, {} as Record<string, { quantity: number; revenue: number }>);

    const topProducts = Object.entries(productStats)
      .map(([name, stats]) => ({ name, ...stats }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);

    // حساب أفضل العملاء
    const customerStats = filteredTransactions.reduce((acc, t) => {
      if (!acc[t.customerName]) {
        acc[t.customerName] = { transactions: 0, total: 0 };
      }
      acc[t.customerName].transactions += 1;
      acc[t.customerName].total += t.totalAmount;
      return acc;
    }, {} as Record<string, { transactions: number; total: number }>);

    const topCustomers = Object.entries(customerStats)
      .map(([name, stats]) => ({ name, ...stats }))
      .sort((a, b) => b.total - a.total)
      .slice(0, 5);

    return {
      totalSales,
      totalTransactions,
      totalPaid,
      totalRemaining,
      topProducts,
      topCustomers,
      filteredTransactions,
    };
  }, [transactions, dateFrom, dateTo, reportGenerated]);

  // وظيفة تحديث التقرير
  const handleGenerateReport = () => {
    if (!dateFrom && !dateTo) {
      toast.error("يرجى تحديد تاريخ البداية أو النهاية على الأقل");
      return;
    }

    setReportGenerated(true);
    toast.success("تم تحديث التقرير بنجاح");
  };

  // وظيفة إعادة تعيين التقرير
  const handleResetReport = () => {
    setDateFrom("");
    setDateTo("");
    setReportGenerated(false);
    toast.info("تم إعادة تعيين التقرير");
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* رأس الصفحة */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-4">
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4 w-full sm:w-auto">
          <Link to="/">
            <Button variant="ghost" size="sm" className="text-xs sm:text-sm">
              <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4" />
              العودة للرئيسية
            </Button>
          </Link>
          <div>
            <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-foreground">التقارير المالية</h1>
            <p className="text-sm sm:text-base text-muted-foreground">تحليل الأداء المالي والمبيعات</p>
          </div>
        </div>
        {reportGenerated && (
          <Button
            variant="outline"
            onClick={handleResetReport}
            className="w-full sm:w-auto text-sm sm:text-base"
          >
            إعادة تعيين
          </Button>
        )}
      </div>

      {/* أدوات التقرير */}
      <Card className="p-4 sm:p-6 shadow-card">
        <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 text-foreground">إعدادات التقرير</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">نوع التقرير</label>
            <Select value={reportType} onValueChange={setReportType}>
              <SelectTrigger className="text-sm">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="custom">تقرير مخصص</SelectItem>
                <SelectItem value="daily">تقرير يومي</SelectItem>
                <SelectItem value="weekly">تقرير أسبوعي</SelectItem>
                <SelectItem value="monthly">تقرير شهري</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">من تاريخ *</label>
            <Input
              type="date"
              value={dateFrom}
              onChange={(e) => setDateFrom(e.target.value)}
              className="text-sm"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">إلى تاريخ</label>
            <Input
              type="date"
              value={dateTo}
              onChange={(e) => setDateTo(e.target.value)}
              className="text-sm"
              placeholder="اختياري - إذا لم يحدد سيكون حتى اليوم"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">إجراءات</label>
            <Button
              variant="gradient"
              className="w-full text-sm sm:text-base"
              onClick={handleGenerateReport}
              disabled={!dateFrom && !dateTo}
            >
              <TrendingUp className="w-3 h-3 sm:w-4 sm:h-4" />
              تحديث التقرير
            </Button>
          </div>
        </div>

        {!reportGenerated && (
          <div className="mt-4 p-3 bg-muted/50 rounded-lg">
            <p className="text-sm text-muted-foreground text-center">
              💡 اختر التواريخ واضغط "تحديث التقرير" لعرض البيانات المالية
            </p>
          </div>
        )}

        {reportGenerated && reportData.filteredTransactions.length === 0 && (
          <div className="mt-4 p-3 bg-warning/10 border border-warning/20 rounded-lg">
            <p className="text-sm text-warning text-center">
              ⚠️ لا توجد معاملات في الفترة المحددة
            </p>
          </div>
        )}
      </Card>

      {/* الإحصائيات الرئيسية */}
      {reportGenerated && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          <Card className="p-4 sm:p-6 shadow-card transition-smooth hover:shadow-elevated">
            <div className="flex items-center justify-between">
              <div className="min-w-0 flex-1">
                <p className="text-xs sm:text-sm text-muted-foreground">إجمالي المبيعات</p>
                <p className="text-lg sm:text-2xl md:text-3xl font-bold text-success numbers-ltr truncate">
                  {reportData.totalSales.toLocaleString()}
                </p>
                <p className="text-xs text-muted-foreground">جنيه مصري</p>
              </div>
              <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-lg gradient-success flex items-center justify-center flex-shrink-0">
                <TrendingUp className="w-5 h-5 sm:w-6 sm:h-6 text-success-foreground" />
              </div>
            </div>
          </Card>

          <Card className="p-4 sm:p-6 shadow-card transition-smooth hover:shadow-elevated">
            <div className="flex items-center justify-between">
              <div className="min-w-0 flex-1">
                <p className="text-xs sm:text-sm text-muted-foreground">عدد المعاملات</p>
                <p className="text-lg sm:text-2xl md:text-3xl font-bold text-primary numbers-ltr">
                  {reportData.totalTransactions}
                </p>
                <p className="text-xs text-muted-foreground">معاملة</p>
              </div>
              <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-lg gradient-primary flex items-center justify-center flex-shrink-0">
                <FileText className="w-5 h-5 sm:w-6 sm:h-6 text-primary-foreground" />
              </div>
            </div>
          </Card>

          <Card className="p-4 sm:p-6 shadow-card transition-smooth hover:shadow-elevated">
            <div className="flex items-center justify-between">
              <div className="min-w-0 flex-1">
                <p className="text-xs sm:text-sm text-muted-foreground">المبلغ المدفوع</p>
                <p className="text-lg sm:text-2xl md:text-3xl font-bold text-secondary-accent numbers-ltr truncate">
                  {reportData.totalPaid.toLocaleString()}
                </p>
                <p className="text-xs text-muted-foreground">جنيه مصري</p>
              </div>
              <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-lg bg-secondary-accent flex items-center justify-center flex-shrink-0">
                <Calendar className="w-5 h-5 sm:w-6 sm:h-6 text-primary-foreground" />
              </div>
            </div>
          </Card>

          <Card className="p-4 sm:p-6 shadow-card transition-smooth hover:shadow-elevated">
            <div className="flex items-center justify-between">
              <div className="min-w-0 flex-1">
                <p className="text-xs sm:text-sm text-muted-foreground">المبلغ المتبقي</p>
                <p className="text-lg sm:text-2xl md:text-3xl font-bold text-destructive numbers-ltr truncate">
                  {reportData.totalRemaining.toLocaleString()}
                </p>
                <p className="text-xs text-muted-foreground">جنيه مصري</p>
              </div>
              <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-lg bg-destructive flex items-center justify-center flex-shrink-0">
                <FileText className="w-5 h-5 sm:w-6 sm:h-6 text-destructive-foreground" />
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* تقارير تفصيلية */}
      {reportGenerated && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
          {/* أفضل المنتجات */}
          <Card className="p-4 sm:p-6 shadow-card">
            <div className="flex items-center gap-2 mb-3 sm:mb-4">
              <Package className="w-5 h-5 text-primary" />
              <h3 className="text-base sm:text-lg font-semibold text-foreground">أفضل المنتجات مبيعاً</h3>
            </div>
            <div className="space-y-3 sm:space-y-4">
              {reportData.topProducts.length === 0 ? (
                <div className="text-center py-6 sm:py-8 text-muted-foreground">
                  <Package className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p className="text-sm sm:text-base">لا توجد بيانات مبيعات في هذه الفترة</p>
                  <p className="text-xs sm:text-sm mt-1">جرب فترة زمنية أخرى</p>
                </div>
              ) : (
                reportData.topProducts.map((product, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg transition-smooth hover:bg-muted/70">
                    <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
                      <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-xs sm:text-sm font-bold flex-shrink-0">
                        {index + 1}
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="font-medium text-sm sm:text-base truncate">{product.name}</p>
                        <p className="text-xs sm:text-sm text-muted-foreground numbers-ltr">
                          {product.quantity.toFixed(1)} وحدة
                        </p>
                      </div>
                    </div>
                    <div className="text-left flex-shrink-0">
                      <p className="font-bold text-success numbers-ltr text-sm sm:text-base">
                        {product.revenue.toLocaleString()} ج.م
                      </p>
                    </div>
                  </div>
                ))
              )}
            </div>
          </Card>

          {/* أفضل العملاء */}
          <Card className="p-4 sm:p-6 shadow-card">
            <div className="flex items-center gap-2 mb-3 sm:mb-4">
              <Users className="w-5 h-5 text-secondary-accent" />
              <h3 className="text-base sm:text-lg font-semibold text-foreground">أفضل العملاء</h3>
            </div>
            <div className="space-y-3 sm:space-y-4">
              {reportData.topCustomers.length === 0 ? (
                <div className="text-center py-6 sm:py-8 text-muted-foreground">
                  <Users className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p className="text-sm sm:text-base">لا توجد بيانات عملاء في هذه الفترة</p>
                  <p className="text-xs sm:text-sm mt-1">جرب فترة زمنية أخرى</p>
                </div>
              ) : (
                reportData.topCustomers.map((customer, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg transition-smooth hover:bg-muted/70">
                    <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
                      <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-secondary-accent text-primary-foreground flex items-center justify-center text-xs sm:text-sm font-bold flex-shrink-0">
                        {index + 1}
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="font-medium text-sm sm:text-base truncate">{customer.name}</p>
                        <p className="text-xs sm:text-sm text-muted-foreground numbers-ltr">
                          {customer.transactions} معاملة
                        </p>
                      </div>
                    </div>
                    <div className="text-left flex-shrink-0">
                      <p className="font-bold text-primary numbers-ltr text-sm sm:text-base">
                        {customer.total.toLocaleString()} ج.م
                      </p>
                    </div>
                  </div>
                ))
              )}
            </div>
          </Card>
        </div>
      )}

      {/* نسبة الدفع */}
      {reportGenerated && reportData.totalSales > 0 && (
        <Card className="p-4 sm:p-6 shadow-card">
          <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 text-foreground">تحليل نسبة الدفع</h3>
          <div className="space-y-3 sm:space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm sm:text-base text-muted-foreground">المبلغ المدفوع</span>
              <span className="font-bold text-success numbers-ltr text-sm sm:text-base">
                {reportData.totalPaid.toLocaleString()} ج.م
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm sm:text-base text-muted-foreground">المبلغ المتبقي (ديون)</span>
              <span className="font-bold text-destructive numbers-ltr text-sm sm:text-base">
                {reportData.totalRemaining.toLocaleString()} ج.م
              </span>
            </div>
            <div className="w-full bg-muted rounded-full h-2 sm:h-3">
              <div
                className="bg-success h-2 sm:h-3 rounded-full transition-smooth"
                style={{
                  width: `${reportData.totalSales > 0 ? (reportData.totalPaid / reportData.totalSales) * 100 : 0}%`
                }}
              ></div>
            </div>
            <div className="flex flex-col sm:flex-row justify-between gap-2 text-xs sm:text-sm text-muted-foreground">
              <span>نسبة الدفع: {reportData.totalSales > 0 ? ((reportData.totalPaid / reportData.totalSales) * 100).toFixed(1) : 0}%</span>
              <span>نسبة الديون: {reportData.totalSales > 0 ? ((reportData.totalRemaining / reportData.totalSales) * 100).toFixed(1) : 0}%</span>
            </div>
          </div>
        </Card>
      )}

      {/* معلومات إضافية عن الفترة */}
      {reportGenerated && (
        <Card className="p-4 sm:p-6 shadow-card bg-gradient-to-r from-primary/5 to-secondary/5">
          <h3 className="text-base sm:text-lg font-semibold mb-3 text-foreground">ملخص الفترة</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">من تاريخ:</span>
              <span className="font-medium">{dateFrom ? new Date(dateFrom).toLocaleDateString('ar-EG') : 'غير محدد'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">إلى تاريخ:</span>
              <span className="font-medium">{dateTo ? new Date(dateTo).toLocaleDateString('ar-EG') : 'حتى اليوم'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">عدد الأيام:</span>
              <span className="font-medium numbers-ltr">
                {dateFrom && dateTo
                  ? Math.ceil((new Date(dateTo).getTime() - new Date(dateFrom).getTime()) / (1000 * 60 * 60 * 24)) + 1
                  : dateFrom
                    ? Math.ceil((new Date().getTime() - new Date(dateFrom).getTime()) / (1000 * 60 * 60 * 24)) + 1
                    : 'غير محدد'
                } يوم
              </span>
            </div>
          </div>
        </Card>
      )}

      {/* قائمة المعاملات التفصيلية */}
      {reportGenerated && reportData.filteredTransactions.length > 0 && (
        <Card className="p-4 sm:p-6 shadow-card">
          <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 text-foreground">
            المعاملات التفصيلية ({reportData.filteredTransactions.length} معاملة)
          </h3>
          <ResponsiveTable
            columns={[
              { key: 'index', label: '#', className: 'font-medium numbers-ltr' },
              { key: 'customerName', label: 'العميل', className: 'font-medium' },
              { key: 'product', label: 'المنتج' },
              {
                key: 'quantity',
                label: 'الكمية',
                className: 'numbers-ltr',
                render: (value: string) => `${value} كيلو`
              },
              {
                key: 'pricePerUnit',
                label: 'السعر/كيلو',
                className: 'numbers-ltr',
                render: (value: number) => `${value} ج.م`
              },
              {
                key: 'totalAmount',
                label: 'المبلغ الكلي',
                className: 'numbers-ltr font-medium',
                render: (value: number) => `${value.toLocaleString()} ج.م`
              },
              {
                key: 'paidAmount',
                label: 'المدفوع',
                className: 'numbers-ltr text-success',
                render: (value: number) => `${value.toLocaleString()} ج.م`
              },
              {
                key: 'remainingAmount',
                label: 'المتبقي',
                className: 'numbers-ltr',
                render: (value: number) => (
                  <span className={value > 0 ? "text-destructive font-medium" : "text-success"}>
                    {value.toLocaleString()} ج.م
                  </span>
                )
              },
              {
                key: 'date',
                label: 'التاريخ',
                className: 'text-muted-foreground',
                render: (value: string) => new Date(value).toLocaleDateString('ar-EG', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric'
                })
              }
            ]}
            data={reportData.filteredTransactions.map((transaction, index) => ({
              ...transaction,
              index: index + 1
            }))}
            emptyMessage="لا توجد معاملات في هذه الفترة"
          />
        </Card>
      )}

      {/* أزرار التصدير */}
      {reportGenerated && (
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
          <Button variant="outline" size="lg" className="w-full sm:w-auto text-sm sm:text-base">
            <FileText className="w-4 h-4 sm:w-5 sm:h-5" />
            تصدير PDF
          </Button>
          <Button variant="outline" size="lg" className="w-full sm:w-auto text-sm sm:text-base">
            <Calendar className="w-4 h-4 sm:w-5 sm:h-5" />
            تصدير Excel
          </Button>
          <Button variant="gradient" size="lg" className="w-full sm:w-auto text-sm sm:text-base">
            <FileText className="w-4 h-4 sm:w-5 sm:h-5" />
            طباعة التقرير
          </Button>
        </div>
      )}
    </div>
  );
};

export default Reports;