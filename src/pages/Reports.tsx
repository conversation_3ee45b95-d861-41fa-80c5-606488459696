import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar, FileText, ArrowRight } from "lucide-react";
import { Link } from "react-router-dom";

const Reports = () => {
  const [dateFrom, setDateFrom] = useState("");
  const [dateTo, setDateTo] = useState("");
  const [reportType, setReportType] = useState("daily");

  // بيانات وهمية للتقارير
  const reportData = {
    daily: {
      totalSales: 12500,
      totalTransactions: 8,
      totalPaid: 10000,
      totalRemaining: 2500,
      topProducts: [
        { name: "طماطم", quantity: 50, revenue: 2500 },
        { name: "خيا<PERSON>", quantity: 30, revenue: 1800 },
        { name: "عنب", quantity: 20, revenue: 4000 },
      ],
      topCustomers: [
        { name: "سوق الفواكه المركزي", transactions: 3, total: 5500 },
        { name: "متجر أحمد الزراعي", transactions: 2, total: 3000 },
        { name: "تجارة محمد للخضار", transactions: 3, total: 4000 },
      ],
    },
    monthly: {
      totalSales: 450000,
      totalTransactions: 145,
      totalPaid: 380000,
      totalRemaining: 70000,
      topProducts: [
        { name: "طماطم", quantity: 800, revenue: 40000 },
        { name: "عنب", quantity: 300, revenue: 60000 },
        { name: "خيار", quantity: 600, revenue: 36000 },
      ],
      topCustomers: [
        { name: "سوق الفواكه المركزي", transactions: 45, total: 180000 },
        { name: "متجر أحمد الزراعي", transactions: 35, total: 120000 },
        { name: "مؤسسة الخير التجارية", transactions: 25, total: 95000 },
      ],
    },
  };

  const currentData = reportData[reportType as keyof typeof reportData];

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link to="/">
            <Button variant="ghost" size="sm">
              <ArrowRight className="w-4 h-4" />
              العودة للرئيسية
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-foreground">التقارير المالية</h1>
            <p className="text-muted-foreground">تحليل الأداء المالي والمبيعات</p>
          </div>
        </div>
      </div>

      {/* أدوات التقرير */}
      <Card className="p-6 shadow-card">
        <h3 className="text-lg font-semibold mb-4 text-foreground">إعدادات التقرير</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">نوع التقرير</label>
            <Select value={reportType} onValueChange={setReportType}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">تقرير يومي</SelectItem>
                <SelectItem value="weekly">تقرير أسبوعي</SelectItem>
                <SelectItem value="monthly">تقرير شهري</SelectItem>
                <SelectItem value="yearly">تقرير سنوي</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">من تاريخ</label>
            <Input
              type="date"
              value={dateFrom}
              onChange={(e) => setDateFrom(e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">إلى تاريخ</label>
            <Input
              type="date"
              value={dateTo}
              onChange={(e) => setDateTo(e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">إجراءات</label>
            <Button variant="gradient" className="w-full">
              <Calendar className="w-4 h-4" />
              تحديث التقرير
            </Button>
          </div>
        </div>
      </Card>

      {/* الإحصائيات الرئيسية */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6 shadow-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">إجمالي المبيعات</p>
              <p className="text-3xl font-bold text-success numbers-ltr">
                {currentData.totalSales.toLocaleString()}
              </p>
              <p className="text-xs text-muted-foreground">جنيه مصري</p>
            </div>
            <div className="w-12 h-12 rounded-lg gradient-success flex items-center justify-center">
              <Calendar className="w-6 h-6 text-success-foreground" />
            </div>
          </div>
        </Card>

        <Card className="p-6 shadow-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">عدد المعاملات</p>
              <p className="text-3xl font-bold text-primary numbers-ltr">
                {currentData.totalTransactions}
              </p>
              <p className="text-xs text-muted-foreground">معاملة</p>
            </div>
            <div className="w-12 h-12 rounded-lg gradient-primary flex items-center justify-center">
              <FileText className="w-6 h-6 text-primary-foreground" />
            </div>
          </div>
        </Card>

        <Card className="p-6 shadow-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">المبلغ المدفوع</p>
              <p className="text-3xl font-bold text-secondary-accent numbers-ltr">
                {currentData.totalPaid.toLocaleString()}
              </p>
              <p className="text-xs text-muted-foreground">جنيه مصري</p>
            </div>
            <div className="w-12 h-12 rounded-lg bg-secondary-accent flex items-center justify-center">
              <Calendar className="w-6 h-6 text-primary-foreground" />
            </div>
          </div>
        </Card>

        <Card className="p-6 shadow-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">المبلغ المتبقي</p>
              <p className="text-3xl font-bold text-destructive numbers-ltr">
                {currentData.totalRemaining.toLocaleString()}
              </p>
              <p className="text-xs text-muted-foreground">جنيه مصري</p>
            </div>
            <div className="w-12 h-12 rounded-lg bg-destructive flex items-center justify-center">
              <FileText className="w-6 h-6 text-destructive-foreground" />
            </div>
          </div>
        </Card>
      </div>

      {/* تقارير تفصيلية */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* أفضل المنتجات */}
        <Card className="p-6 shadow-card">
          <h3 className="text-lg font-semibold mb-4 text-foreground">أفضل المنتجات مبيعاً</h3>
          <div className="space-y-4">
            {currentData.topProducts.map((product, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm font-bold">
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium">{product.name}</p>
                    <p className="text-sm text-muted-foreground numbers-ltr">
                      {product.quantity} كيلو
                    </p>
                  </div>
                </div>
                <div className="text-left">
                  <p className="font-bold text-success numbers-ltr">
                    {product.revenue.toLocaleString()} ج.م
                  </p>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* أفضل العملاء */}
        <Card className="p-6 shadow-card">
          <h3 className="text-lg font-semibold mb-4 text-foreground">أفضل العملاء</h3>
          <div className="space-y-4">
            {currentData.topCustomers.map((customer, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-secondary-accent text-primary-foreground flex items-center justify-center text-sm font-bold">
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium">{customer.name}</p>
                    <p className="text-sm text-muted-foreground numbers-ltr">
                      {customer.transactions} معاملة
                    </p>
                  </div>
                </div>
                <div className="text-left">
                  <p className="font-bold text-primary numbers-ltr">
                    {customer.total.toLocaleString()} ج.م
                  </p>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* نسبة الدفع */}
      <Card className="p-6 shadow-card">
        <h3 className="text-lg font-semibold mb-4 text-foreground">نسبة الدفع</h3>
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-muted-foreground">المبلغ المدفوع</span>
            <span className="font-bold text-success numbers-ltr">
              {currentData.totalPaid.toLocaleString()} ج.م
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-muted-foreground">المبلغ المتبقي (ديون)</span>
            <span className="font-bold text-destructive numbers-ltr">
              {currentData.totalRemaining.toLocaleString()} ج.م
            </span>
          </div>
          <div className="w-full bg-muted rounded-full h-3">
            <div 
              className="bg-success h-3 rounded-full transition-smooth"
              style={{ 
                width: `${(currentData.totalPaid / currentData.totalSales) * 100}%` 
              }}
            ></div>
          </div>
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>نسبة الدفع: {((currentData.totalPaid / currentData.totalSales) * 100).toFixed(1)}%</span>
            <span>نسبة الديون: {((currentData.totalRemaining / currentData.totalSales) * 100).toFixed(1)}%</span>
          </div>
        </div>
      </Card>

      {/* أزرار التصدير */}
      <div className="flex gap-4">
        <Button variant="outline" size="lg">
          <FileText className="w-5 h-5" />
          تصدير PDF
        </Button>
        <Button variant="outline" size="lg">
          <Calendar className="w-5 h-5" />
          تصدير Excel
        </Button>
        <Button variant="gradient" size="lg">
          <FileText className="w-5 h-5" />
          طباعة التقرير
        </Button>
      </div>
    </div>
  );
};

export default Reports;