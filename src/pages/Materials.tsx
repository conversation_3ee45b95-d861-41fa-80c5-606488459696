import React, { useState } from 'react';
import { useData } from '@/contexts/DataContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  Package, 
  AlertTriangle, 
  TrendingUp, 
  Search,
  Edit,
  Trash2,
  Eye,
  BarChart3
} from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import type { Material } from '@/contexts/DataContext';

const Materials: React.FC = () => {
  const { materials, addMaterial, updateMaterial, deleteMaterial, getMaterialStats } = useData();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [selectedMaterial, setSelectedMaterial] = useState<Material | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    unit: '',
    availableQuantity: 0,
    unitCost: 0,
    supplier: '',
    location: '',
    description: ''
  });

  const stats = getMaterialStats();

  // فلترة الخامات
  const filteredMaterials = materials.filter(material => {
    const matchesSearch = material.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         material.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         material.supplier.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || material.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  // الحصول على الفئات الفريدة
  const categories = Array.from(new Set(materials.map(m => m.category))).filter(Boolean);

  // إعادة تعيين النموذج
  const resetForm = () => {
    setFormData({
      name: '',
      category: '',
      unit: '',
      availableQuantity: 0,
      unitCost: 0,
      supplier: '',
      location: '',
      description: ''
    });
  };

  // إضافة خامة جديدة
  const handleAddMaterial = async () => {
    try {
      if (!formData.name || !formData.category || !formData.unit) {
        toast.error('يرجى ملء جميع الحقول المطلوبة');
        return;
      }

      await addMaterial(formData);
      toast.success('تم إضافة الخامة بنجاح');
      setIsAddDialogOpen(false);
      resetForm();
    } catch (error) {
      console.error('Error adding material:', error);
      toast.error('حدث خطأ في إضافة الخامة');
    }
  };

  // تحديث خامة
  const handleUpdateMaterial = async () => {
    try {
      if (!selectedMaterial?.id) return;

      await updateMaterial(selectedMaterial.id, formData);
      toast.success('تم تحديث الخامة بنجاح');
      setIsEditDialogOpen(false);
      setSelectedMaterial(null);
      resetForm();
    } catch (error) {
      console.error('Error updating material:', error);
      toast.error('حدث خطأ في تحديث الخامة');
    }
  };

  // حذف خامة
  const handleDeleteMaterial = async (id: string) => {
    try {
      if (window.confirm('هل أنت متأكد من حذف هذه الخامة؟')) {
        await deleteMaterial(id);
        toast.success('تم حذف الخامة بنجاح');
      }
    } catch (error) {
      console.error('Error deleting material:', error);
      toast.error('حدث خطأ في حذف الخامة');
    }
  };

  // فتح نموذج التعديل
  const openEditDialog = (material: Material) => {
    setSelectedMaterial(material);
    setFormData({
      name: material.name,
      category: material.category,
      unit: material.unit,
      availableQuantity: material.availableQuantity,
      unitCost: material.unitCost,
      supplier: material.supplier,
      location: material.location,
      description: material.description || ''
    });
    setIsEditDialogOpen(true);
  };

  // فتح نموذج التفاصيل
  const openDetailsDialog = (material: Material) => {
    setSelectedMaterial(material);
    setIsDetailsDialogOpen(true);
  };

  // تحديد لون حالة المخزون
  const getStockStatus = (material: Material) => {
    if (material.currentStock <= 10) {
      return { color: 'destructive', text: 'مخزون منخفض' };
    } else if (material.availableQuantity > 50) {
      return { color: 'default', text: 'مخزون جيد' };
    } else {
      return { color: 'secondary', text: 'مخزون متوسط' };
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* العنوان والإحصائيات */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">إدارة الخامات</h1>
          <p className="text-muted-foreground">إدارة المخزون والخامات المستخدمة في الإنتاج</p>
        </div>
        
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 ml-2" />
              إضافة خامة جديدة
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-[95vw] sm:max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-lg sm:text-xl">إضافة خامة جديدة</DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-1 gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">اسم الخامة *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="أدخل اسم الخامة"
                  className="text-base"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="category">الفئة *</Label>
                <Input
                  id="category"
                  value={formData.category}
                  onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                  placeholder="أدخل فئة الخامة"
                  className="text-base"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="unit">وحدة القياس *</Label>
                <Select value={formData.unit} onValueChange={(value) => setFormData(prev => ({ ...prev, unit: value }))}>
                  <SelectTrigger className="text-base">
                    <SelectValue placeholder="اختر وحدة القياس" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="كيلو">كيلو</SelectItem>
                    <SelectItem value="جرام">جرام</SelectItem>
                    <SelectItem value="قطعة">قطعة</SelectItem>
                    <SelectItem value="عبوة">عبوة</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="availableQuantity">عدد المتوفر من الخامة *</Label>
                <Input
                  id="availableQuantity"
                  type="number"
                  inputMode="decimal"
                  value={formData.availableQuantity}
                  onChange={(e) => setFormData(prev => ({ ...prev, availableQuantity: parseFloat(e.target.value) || 0 }))}
                  placeholder="0"
                  min="0"
                  step="0.01"
                  className="text-base"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="unitCost">تكلفة الوحدة (جنيه)</Label>
                <Input
                  id="unitCost"
                  type="number"
                  inputMode="decimal"
                  value={formData.unitCost}
                  onChange={(e) => setFormData(prev => ({ ...prev, unitCost: parseFloat(e.target.value) || 0 }))}
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                  className="text-base"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="totalCost">إجمالي التكلفة (جنيه)</Label>
                <Input
                  id="totalCost"
                  type="number"
                  value={(formData.availableQuantity * formData.unitCost).toFixed(2)}
                  readOnly
                  className="bg-gray-100 dark:bg-gray-800 text-base"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="supplier">المورد</Label>
                <Input
                  id="supplier"
                  value={formData.supplier}
                  onChange={(e) => setFormData(prev => ({ ...prev, supplier: e.target.value }))}
                  placeholder="اسم المورد"
                  className="text-base"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="location">موقع التخزين</Label>
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                  placeholder="موقع التخزين"
                  className="text-base"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">الوصف</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="وصف الخامة (اختياري)"
                  rows={3}
                  className="text-base resize-none"
                />
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row justify-end gap-3 mt-6 pt-4 border-t">
              <Button
                variant="outline"
                onClick={() => setIsAddDialogOpen(false)}
                className="w-full sm:w-auto min-h-[44px]"
              >
                رجوع
              </Button>
              <Button
                onClick={handleAddMaterial}
                className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto min-h-[44px]"
              >
                إضافة الخامة
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* بطاقات الإحصائيات */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الخامات</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalMaterials}</div>
            <p className="text-xs text-muted-foreground">خامة مختلفة</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">مخزون منخفض</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.lowStockMaterials}</div>
            <p className="text-xs text-muted-foreground">خامة تحتاج تجديد</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">قيمة المخزون</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalStockValue.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">جنيه مصري</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي المتوفر</CardTitle>
            <BarChart3 className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalAvailableQuantity}</div>
            <p className="text-xs text-muted-foreground">وحدة متوفرة</p>
          </CardContent>
        </Card>
      </div>

      {/* البحث والفلترة */}
      <Card>
        <CardHeader>
          <CardTitle>البحث والفلترة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في الخامات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="w-full md:w-48">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="جميع الفئات" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الفئات</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category} value={category}>{category}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* جدول الخامات */}
      <Card>
        <CardHeader>
          <CardTitle>قائمة الخامات ({filteredMaterials.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {filteredMaterials.length === 0 ? (
            <div className="text-center py-8">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">لا توجد خامات مطابقة للبحث</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-right p-3">اسم الخامة</th>
                    <th className="text-right p-3">الفئة</th>
                    <th className="text-right p-3">المخزون الحالي</th>
                    <th className="text-right p-3">الكمية المتوفرة</th>
                    <th className="text-right p-3">الوحدة</th>
                    <th className="text-right p-3">التكلفة</th>
                    <th className="text-right p-3">المورد</th>
                    <th className="text-right p-3">الحالة</th>
                    <th className="text-right p-3">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredMaterials.map((material) => {
                    const stockStatus = getStockStatus(material);
                    return (
                      <tr key={material.id} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="p-3 font-medium">{material.name}</td>
                        <td className="p-3">{material.category}</td>
                        <td className="p-3">
                          <span className="font-mono">{material.currentStock}</span>
                        </td>
                        <td className="p-3">
                          <span className="font-mono">{material.availableQuantity}</span>
                        </td>
                        <td className="p-3">{material.unit}</td>
                        <td className="p-3">
                          <span className="font-mono">{material.unitCost.toFixed(2)} جنيه</span>
                        </td>
                        <td className="p-3">{material.supplier}</td>
                        <td className="p-3">
                          <Badge variant={stockStatus.color as any}>
                            {stockStatus.text}
                          </Badge>
                        </td>
                        <td className="p-3">
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openDetailsDialog(material)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openEditDialog(material)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => material.id && handleDeleteMaterial(material.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* نموذج التعديل */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-[95vw] sm:max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-lg sm:text-xl">تعديل الخامة</DialogTitle>
          </DialogHeader>
          <div className="grid grid-cols-1 gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">اسم الخامة *</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="أدخل اسم الخامة"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-category">الفئة *</Label>
              <Input
                id="edit-category"
                value={formData.category}
                onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                placeholder="أدخل فئة الخامة"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-unit">وحدة القياس *</Label>
              <Select value={formData.unit} onValueChange={(value) => setFormData(prev => ({ ...prev, unit: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر وحدة القياس" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="كيلو">كيلو</SelectItem>
                  <SelectItem value="جرام">جرام</SelectItem>
                  <SelectItem value="قطعة">قطعة</SelectItem>
                  <SelectItem value="عبوة">عبوة</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-unitCost">تكلفة الوحدة (جنيه)</Label>
              <Input
                id="edit-unitCost"
                type="number"
                value={formData.unitCost}
                onChange={(e) => setFormData(prev => ({ ...prev, unitCost: parseFloat(e.target.value) || 0 }))}
                placeholder="0.00"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-availableQuantity">الكمية المتوفرة</Label>
              <Input
                id="edit-availableQuantity"
                type="number"
                value={formData.availableQuantity}
                onChange={(e) => setFormData(prev => ({ ...prev, availableQuantity: parseInt(e.target.value) || 0 }))}
                placeholder="0"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-totalCost">إجمالي التكلفة (جنيه)</Label>
              <Input
                id="edit-totalCost"
                type="number"
                value={(formData.availableQuantity * formData.unitCost).toFixed(2)}
                readOnly
                className="bg-gray-100 dark:bg-gray-800"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-supplier">المورد</Label>
              <Input
                id="edit-supplier"
                value={formData.supplier}
                onChange={(e) => setFormData(prev => ({ ...prev, supplier: e.target.value }))}
                placeholder="اسم المورد"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-location">موقع التخزين</Label>
              <Input
                id="edit-location"
                value={formData.location}
                onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                placeholder="موقع التخزين"
              />
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="edit-description">الوصف</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="وصف الخامة (اختياري)"
                rows={3}
              />
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-end gap-3 mt-6 pt-4 border-t">
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
              className="w-full sm:w-auto min-h-[44px]"
            >
              رجوع
            </Button>
            <Button
              onClick={handleUpdateMaterial}
              className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto min-h-[44px]"
            >
              حفظ التغييرات
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* نموذج التفاصيل */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>تفاصيل الخامة</DialogTitle>
          </DialogHeader>
          {selectedMaterial && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">اسم الخامة</Label>
                  <p className="text-lg font-semibold">{selectedMaterial.name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">الفئة</Label>
                  <p className="text-lg">{selectedMaterial.category}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">المخزون الحالي</Label>
                  <p className="text-lg font-mono">{selectedMaterial.currentStock} {selectedMaterial.unit}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">تكلفة الوحدة</Label>
                  <p className="text-lg font-mono">{selectedMaterial.unitCost.toFixed(2)} جنيه</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">الكمية المتوفرة</Label>
                  <p className="text-lg">{selectedMaterial.availableQuantity} {selectedMaterial.unit}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">إجمالي التكلفة</Label>
                  <p className="text-lg font-mono">{(selectedMaterial.availableQuantity * selectedMaterial.unitCost).toFixed(2)} جنيه</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">المورد</Label>
                  <p className="text-lg">{selectedMaterial.supplier}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">موقع التخزين</Label>
                  <p className="text-lg">{selectedMaterial.location}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">إجمالي المشتريات</Label>
                  <p className="text-lg font-mono">{selectedMaterial.totalPurchased} {selectedMaterial.unit}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">إجمالي المستخدم</Label>
                  <p className="text-lg font-mono">{selectedMaterial.totalUsed} {selectedMaterial.unit}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">إجمالي التكلفة</Label>
                  <p className="text-lg font-mono">{selectedMaterial.totalCost.toFixed(2)} جنيه</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">آخر تحديث</Label>
                  <p className="text-lg">{selectedMaterial.lastUpdated}</p>
                </div>
              </div>

              {selectedMaterial.description && (
                <div>
                  <Label className="text-sm font-medium text-gray-500">الوصف</Label>
                  <p className="text-lg">{selectedMaterial.description}</p>
                </div>
              )}

              <div className="flex justify-center">
                <Badge variant={getStockStatus(selectedMaterial).color as any} className="text-lg px-4 py-2">
                  {getStockStatus(selectedMaterial).text}
                </Badge>
              </div>
            </div>
          )}

          <div className="flex justify-end mt-6">
            <Button variant="outline" onClick={() => setIsDetailsDialogOpen(false)}>
              إغلاق
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Materials;
