import React, { useState } from 'react';
import { useData } from '@/contexts/DataContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  ArrowUp, 
  ArrowDown, 
  Search,
  Edit,
  Trash2,
  Eye,
  Package
} from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import type { StockTransaction } from '@/contexts/DataContext';

const StockTransactions: React.FC = () => {
  const { 
    materials, 
    stockTransactions, 
    addStockTransaction, 
    updateStockTransaction, 
    deleteStockTransaction 
  } = useData();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedMaterial, setSelectedMaterial] = useState('all');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<StockTransaction | null>(null);
  const [formData, setFormData] = useState({
    materialId: '',
    materialName: '',
    type: 'in' as 'in' | 'out',
    quantity: 0,
    unitCost: 0,
    totalCost: 0,
    reason: '',
    reference: '',
    date: new Date().toISOString().split('T')[0],
    time: new Date().toTimeString().slice(0, 5),
    notes: ''
  });

  // فلترة حركات المخزون
  const filteredTransactions = stockTransactions.filter(transaction => {
    const matchesSearch = transaction.materialName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transaction.reason.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (transaction.reference && transaction.reference.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesType = selectedType === 'all' || transaction.type === selectedType;
    const matchesMaterial = selectedMaterial === 'all' || transaction.materialId === selectedMaterial;
    return matchesSearch && matchesType && matchesMaterial;
  });

  // إعادة تعيين النموذج
  const resetForm = () => {
    setFormData({
      materialId: '',
      materialName: '',
      type: 'in',
      quantity: 0,
      unitCost: 0,
      totalCost: 0,
      reason: '',
      reference: '',
      date: new Date().toISOString().split('T')[0],
      time: new Date().toTimeString().slice(0, 5),
      notes: ''
    });
  };

  // تحديث اسم الخامة عند تغيير المعرف
  const handleMaterialChange = (materialId: string) => {
    const material = materials.find(m => m.id === materialId);
    setFormData(prev => ({
      ...prev,
      materialId,
      materialName: material?.name || '',
      unitCost: material?.unitCost || 0
    }));
  };

  // تحديث التكلفة الإجمالية
  const updateTotalCost = (quantity: number, unitCost: number) => {
    setFormData(prev => ({
      ...prev,
      quantity,
      unitCost,
      totalCost: quantity * unitCost
    }));
  };

  // إضافة حركة مخزون جديدة
  const handleAddTransaction = async () => {
    try {
      if (!formData.materialId || !formData.reason || formData.quantity <= 0) {
        toast.error('يرجى ملء جميع الحقول المطلوبة');
        return;
      }

      await addStockTransaction(formData);
      toast.success('تم إضافة حركة المخزون بنجاح');
      setIsAddDialogOpen(false);
      resetForm();
    } catch (error: any) {
      console.error('Error adding stock transaction:', error);
      if (error.message === 'Insufficient stock') {
        toast.error('المخزون غير كافي لهذه العملية');
      } else {
        toast.error('حدث خطأ في إضافة حركة المخزون');
      }
    }
  };

  // تحديث حركة مخزون
  const handleUpdateTransaction = async () => {
    try {
      if (!selectedTransaction?.id) return;

      await updateStockTransaction(selectedTransaction.id, formData);
      toast.success('تم تحديث حركة المخزون بنجاح');
      setIsEditDialogOpen(false);
      setSelectedTransaction(null);
      resetForm();
    } catch (error) {
      console.error('Error updating stock transaction:', error);
      toast.error('حدث خطأ في تحديث حركة المخزون');
    }
  };

  // حذف حركة مخزون
  const handleDeleteTransaction = async (id: string) => {
    try {
      if (window.confirm('هل أنت متأكد من حذف هذه الحركة؟')) {
        await deleteStockTransaction(id);
        toast.success('تم حذف حركة المخزون بنجاح');
      }
    } catch (error) {
      console.error('Error deleting stock transaction:', error);
      toast.error('حدث خطأ في حذف حركة المخزون');
    }
  };

  // فتح نموذج التعديل
  const openEditDialog = (transaction: StockTransaction) => {
    setSelectedTransaction(transaction);
    setFormData({
      materialId: transaction.materialId,
      materialName: transaction.materialName,
      type: transaction.type,
      quantity: transaction.quantity,
      unitCost: transaction.unitCost || 0,
      totalCost: transaction.totalCost || 0,
      reason: transaction.reason,
      reference: transaction.reference || '',
      date: transaction.date,
      time: transaction.time,
      notes: transaction.notes || ''
    });
    setIsEditDialogOpen(true);
  };

  // فتح نموذج التفاصيل
  const openDetailsDialog = (transaction: StockTransaction) => {
    setSelectedTransaction(transaction);
    setIsDetailsDialogOpen(true);
  };

  return (
    <div className="space-y-6 p-6">
      {/* العنوان */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">حركات المخزون</h1>
          <p className="text-muted-foreground">إدارة حركات دخول وخروج الخامات من المخزون</p>
        </div>
        
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 ml-2" />
              إضافة حركة جديدة
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>إضافة حركة مخزون جديدة</DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="material">الخامة *</Label>
                <Select value={formData.materialId} onValueChange={handleMaterialChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الخامة" />
                  </SelectTrigger>
                  <SelectContent>
                    {materials.map(material => (
                      <SelectItem key={material.id} value={material.id || ''}>
                        {material.name} - {material.currentStock} {material.unit}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="type">نوع الحركة *</Label>
                <Select value={formData.type} onValueChange={(value: 'in' | 'out') => setFormData(prev => ({ ...prev, type: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر نوع الحركة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="in">دخول (شراء)</SelectItem>
                    <SelectItem value="out">خروج (استخدام)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="quantity">الكمية *</Label>
                <Input
                  id="quantity"
                  type="number"
                  value={formData.quantity}
                  onChange={(e) => updateTotalCost(parseFloat(e.target.value) || 0, formData.unitCost)}
                  placeholder="0"
                />
              </div>
              
              {formData.type === 'in' && (
                <div className="space-y-2">
                  <Label htmlFor="unitCost">تكلفة الوحدة (جنيه)</Label>
                  <Input
                    id="unitCost"
                    type="number"
                    value={formData.unitCost}
                    onChange={(e) => updateTotalCost(formData.quantity, parseFloat(e.target.value) || 0)}
                    placeholder="0.00"
                  />
                </div>
              )}
              
              {formData.type === 'in' && (
                <div className="space-y-2">
                  <Label htmlFor="totalCost">التكلفة الإجمالية</Label>
                  <Input
                    id="totalCost"
                    type="number"
                    value={formData.totalCost}
                    readOnly
                    className="bg-gray-100"
                  />
                </div>
              )}
              
              <div className="space-y-2">
                <Label htmlFor="reason">سبب الحركة *</Label>
                <Input
                  id="reason"
                  value={formData.reason}
                  onChange={(e) => setFormData(prev => ({ ...prev, reason: e.target.value }))}
                  placeholder="مثال: شراء جديد، استخدام في الإنتاج"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="reference">المرجع</Label>
                <Input
                  id="reference"
                  value={formData.reference}
                  onChange={(e) => setFormData(prev => ({ ...prev, reference: e.target.value }))}
                  placeholder="رقم الفاتورة أو أمر العمل"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="date">التاريخ</Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="time">الوقت</Label>
                <Input
                  id="time"
                  type="time"
                  value={formData.time}
                  onChange={(e) => setFormData(prev => ({ ...prev, time: e.target.value }))}
                />
              </div>
              
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="notes">ملاحظات</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="ملاحظات إضافية (اختياري)"
                  rows={3}
                />
              </div>
            </div>
            
            <div className="flex justify-end gap-2 mt-6">
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                إلغاء
              </Button>
              <Button onClick={handleAddTransaction} className="bg-blue-600 hover:bg-blue-700">
                إضافة الحركة
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* البحث والفلترة */}
      <Card>
        <CardHeader>
          <CardTitle>البحث والفلترة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في حركات المخزون..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="w-full md:w-48">
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger>
                  <SelectValue placeholder="نوع الحركة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحركات</SelectItem>
                  <SelectItem value="in">دخول</SelectItem>
                  <SelectItem value="out">خروج</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="w-full md:w-48">
              <Select value={selectedMaterial} onValueChange={setSelectedMaterial}>
                <SelectTrigger>
                  <SelectValue placeholder="الخامة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الخامات</SelectItem>
                  {materials.map(material => (
                    <SelectItem key={material.id} value={material.id || ''}>{material.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* جدول حركات المخزون */}
      <Card>
        <CardHeader>
          <CardTitle>حركات المخزون ({filteredTransactions.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {filteredTransactions.length === 0 ? (
            <div className="text-center py-8">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">لا توجد حركات مخزون مطابقة للبحث</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-right p-3">التاريخ</th>
                    <th className="text-right p-3">الخامة</th>
                    <th className="text-right p-3">النوع</th>
                    <th className="text-right p-3">الكمية</th>
                    <th className="text-right p-3">التكلفة</th>
                    <th className="text-right p-3">السبب</th>
                    <th className="text-right p-3">المرجع</th>
                    <th className="text-right p-3">الرصيد بعد</th>
                    <th className="text-right p-3">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredTransactions.map((transaction) => (
                    <tr key={transaction.id} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td className="p-3">
                        <div>
                          <div className="font-medium">{transaction.date}</div>
                          <div className="text-sm text-gray-500">{transaction.time}</div>
                        </div>
                      </td>
                      <td className="p-3 font-medium">{transaction.materialName}</td>
                      <td className="p-3">
                        <Badge variant={transaction.type === 'in' ? 'default' : 'secondary'}>
                          {transaction.type === 'in' ? (
                            <><ArrowUp className="h-3 w-3 ml-1" /> دخول</>
                          ) : (
                            <><ArrowDown className="h-3 w-3 ml-1" /> خروج</>
                          )}
                        </Badge>
                      </td>
                      <td className="p-3">
                        <span className="font-mono">{transaction.quantity}</span>
                      </td>
                      <td className="p-3">
                        {transaction.totalCost ? (
                          <span className="font-mono">{transaction.totalCost.toFixed(2)} جنيه</span>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </td>
                      <td className="p-3">{transaction.reason}</td>
                      <td className="p-3">{transaction.reference || '-'}</td>
                      <td className="p-3">
                        <span className="font-mono">{transaction.balanceAfter}</span>
                      </td>
                      <td className="p-3">
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openDetailsDialog(transaction)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openEditDialog(transaction)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => transaction.id && handleDeleteTransaction(transaction.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* نموذج التفاصيل */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>تفاصيل حركة المخزون</DialogTitle>
          </DialogHeader>
          {selectedTransaction && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">الخامة</Label>
                  <p className="text-lg font-semibold">{selectedTransaction.materialName}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">نوع الحركة</Label>
                  <Badge variant={selectedTransaction.type === 'in' ? 'default' : 'secondary'} className="text-lg">
                    {selectedTransaction.type === 'in' ? (
                      <><ArrowUp className="h-4 w-4 ml-1" /> دخول</>
                    ) : (
                      <><ArrowDown className="h-4 w-4 ml-1" /> خروج</>
                    )}
                  </Badge>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">الكمية</Label>
                  <p className="text-lg font-mono">{selectedTransaction.quantity}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">تكلفة الوحدة</Label>
                  <p className="text-lg font-mono">
                    {selectedTransaction.unitCost ? `${selectedTransaction.unitCost.toFixed(2)} جنيه` : '-'}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">التكلفة الإجمالية</Label>
                  <p className="text-lg font-mono">
                    {selectedTransaction.totalCost ? `${selectedTransaction.totalCost.toFixed(2)} جنيه` : '-'}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">الرصيد بعد العملية</Label>
                  <p className="text-lg font-mono">{selectedTransaction.balanceAfter}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">السبب</Label>
                  <p className="text-lg">{selectedTransaction.reason}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">المرجع</Label>
                  <p className="text-lg">{selectedTransaction.reference || '-'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">التاريخ</Label>
                  <p className="text-lg">{selectedTransaction.date}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">الوقت</Label>
                  <p className="text-lg">{selectedTransaction.time}</p>
                </div>
              </div>

              {selectedTransaction.notes && (
                <div>
                  <Label className="text-sm font-medium text-gray-500">الملاحظات</Label>
                  <p className="text-lg">{selectedTransaction.notes}</p>
                </div>
              )}
            </div>
          )}

          <div className="flex justify-end mt-6">
            <Button variant="outline" onClick={() => setIsDetailsDialogOpen(false)}>
              إغلاق
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default StockTransactions;
